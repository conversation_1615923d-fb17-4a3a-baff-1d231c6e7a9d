"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/process/page",{

/***/ "(app-pages-browser)/./app/process/page.tsx":
/*!******************************!*\
  !*** ./app/process/page.tsx ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProcessPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_resizable__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/resizable */ \"(app-pages-browser)/./components/ui/resizable.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart4_BookOpen_FileLineChartIcon_FileText_FlashlightIcon_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart4,BookOpen,FileLineChartIcon,FileText,FlashlightIcon,MessageSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart4_BookOpen_FileLineChartIcon_FileText_FlashlightIcon_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart4,BookOpen,FileLineChartIcon,FileText,FlashlightIcon,MessageSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-chart-line.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart4_BookOpen_FileLineChartIcon_FileText_FlashlightIcon_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart4,BookOpen,FileLineChartIcon,FileText,FlashlightIcon,MessageSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart4_BookOpen_FileLineChartIcon_FileText_FlashlightIcon_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart4,BookOpen,FileLineChartIcon,FileText,FlashlightIcon,MessageSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/flashlight.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart4_BookOpen_FileLineChartIcon_FileText_FlashlightIcon_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart4,BookOpen,FileLineChartIcon,FileText,FlashlightIcon,MessageSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart4_BookOpen_FileLineChartIcon_FileText_FlashlightIcon_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart4,BookOpen,FileLineChartIcon,FileText,FlashlightIcon,MessageSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column-increasing.js\");\n/* harmony import */ var _components_upload_content__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/upload-content */ \"(app-pages-browser)/./components/upload-content.tsx\");\n/* harmony import */ var _components_paste_content__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/paste-content */ \"(app-pages-browser)/./components/paste-content.tsx\");\n/* harmony import */ var _components_record_content__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/record-content */ \"(app-pages-browser)/./components/record-content.tsx\");\n/* harmony import */ var _components_chat_interface__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/chat-interface */ \"(app-pages-browser)/./components/chat-interface.tsx\");\n/* harmony import */ var _components_performance_dashboard__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/performance-dashboard */ \"(app-pages-browser)/./components/performance-dashboard.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_layout_with_sidebar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/layout-with-sidebar */ \"(app-pages-browser)/./components/layout-with-sidebar.tsx\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/theme-provider */ \"(app-pages-browser)/./components/theme-provider.tsx\");\n/* harmony import */ var _components_blueprint_interface__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/blueprint-interface */ \"(app-pages-browser)/./components/blueprint-interface.tsx\");\n/* harmony import */ var _components_flowchart_interface__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/flowchart-interface */ \"(app-pages-browser)/./components/flowchart-interface.tsx\");\n/* harmony import */ var _components_flashcards_interface__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/flashcards-interface */ \"(app-pages-browser)/./components/flashcards-interface.tsx\");\n/* harmony import */ var _components_summary_interface__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/summary-interface */ \"(app-pages-browser)/./components/summary-interface.tsx\");\n/* harmony import */ var _components_chapters_interface__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/chapters-interface */ \"(app-pages-browser)/./components/chapters-interface.tsx\");\n/* harmony import */ var _components_loading_overlay__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/loading-overlay */ \"(app-pages-browser)/./components/loading-overlay.tsx\");\n/* harmony import */ var _components_file_preview__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/file-preview */ \"(app-pages-browser)/./components/file-preview.tsx\");\n/* harmony import */ var _components_session_time_display__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/session-time-display */ \"(app-pages-browser)/./components/session-time-display.tsx\");\n/* harmony import */ var _hooks_use_document_time__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/hooks/use-document-time */ \"(app-pages-browser)/./hooks/use-document-time.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ProcessPage() {\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const type = searchParams.get(\"type\") || \"upload\";\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"chat\");\n    const [showFullScreen, setShowFullScreen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [uploadedData, setUploadedData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { theme } = (0,_components_theme_provider__WEBPACK_IMPORTED_MODULE_11__.useTheme)();\n    const tabsListRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // State preservation\n    const [chatState, setChatState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [summaryState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [chapterState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [documentDetails, setDocumentDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProcessPage.useEffect\": ()=>{\n            document.body.classList.add(\"page-transition\");\n            // Check if we have a document ID (either from upload or clicking existing document)\n            const documentId = searchParams.get(\"documentId\");\n            if (documentId) {\n                const docId = parseInt(documentId);\n                // If it's a new upload, show processing\n                if (type === \"upload\") {\n                    setIsProcessing(true);\n                    // Check document processing status\n                    checkDocumentStatus(docId);\n                }\n                // Always load document details for preview\n                loadDocumentDetails(docId);\n            }\n            const uploadedFilesStr = localStorage.getItem(\"uploadedFiles\");\n            if (uploadedFilesStr && type === \"upload\") {\n                try {\n                    const uploadedFiles = JSON.parse(uploadedFilesStr);\n                    if (uploadedFiles.length > 0) {\n                        var _file_name_split_pop;\n                        const file = uploadedFiles[0];\n                        const storedDocumentId = localStorage.getItem(\"currentDocumentId\");\n                        setUploadedData({\n                            type: \"file\",\n                            name: file.name,\n                            size: file.size,\n                            fileType: file.type || ((_file_name_split_pop = file.name.split('.').pop()) === null || _file_name_split_pop === void 0 ? void 0 : _file_name_split_pop.toLowerCase()),\n                            documentId: file.documentId || (storedDocumentId ? parseInt(storedDocumentId) : undefined)\n                        });\n                        setShowFullScreen(false);\n                    }\n                } catch (error) {\n                    console.error(\"Error parsing uploaded files:\", error);\n                }\n            } else if (type === \"paste\") {\n                const pastedContentStr = localStorage.getItem(\"pastedContent\");\n                if (pastedContentStr) {\n                    try {\n                        const pastedContent = JSON.parse(pastedContentStr);\n                        setUploadedData({\n                            type: \"text\",\n                            content: pastedContent.content || pastedContent.url || \"Pasted content\"\n                        });\n                        setShowFullScreen(false);\n                    } catch (error) {\n                        console.error(\"Error parsing pasted content:\", error);\n                    }\n                }\n            } else if (type === \"record\") {\n                const recordedAudioStr = localStorage.getItem(\"recordedAudio\");\n                if (recordedAudioStr) {\n                    try {\n                        const recordedAudio = JSON.parse(recordedAudioStr);\n                        setUploadedData({\n                            type: \"audio\",\n                            name: \"Recording-\" + new Date().toISOString().split(\"T\")[0] + \".wav\",\n                            duration: recordedAudio.duration || \"00:00\"\n                        });\n                        setShowFullScreen(false);\n                    } catch (error) {\n                        console.error(\"Error parsing recorded audio:\", error);\n                    }\n                }\n            }\n            return ({\n                \"ProcessPage.useEffect\": ()=>{\n                    document.body.classList.remove(\"page-transition\");\n                }\n            })[\"ProcessPage.useEffect\"];\n        }\n    }[\"ProcessPage.useEffect\"], [\n        type,\n        searchParams\n    ]);\n    const loadDocumentDetails = async (documentId)=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_21__.documentApi.getDocumentDetails(documentId);\n            setDocumentDetails(response);\n            // Create file preview data from document details\n            if (response && response.file) {\n                var _response_title_split_pop;\n                const fileExtension = (_response_title_split_pop = response.title.split('.').pop()) === null || _response_title_split_pop === void 0 ? void 0 : _response_title_split_pop.toLowerCase();\n                const fileData = {\n                    type: \"file\",\n                    name: response.title,\n                    size: \"Unknown\",\n                    fileType: fileExtension || 'unknown',\n                    documentId: documentId // Store document ID for FilePreview component\n                };\n                setUploadedData(fileData);\n                setShowFullScreen(false);\n            }\n        } catch (error) {\n            console.error('Error loading document details:', error);\n        }\n    };\n    const checkDocumentStatus = async (documentId)=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_21__.documentApi.getDocumentStatus(documentId);\n            if (response.processing_status === 'completed') {\n                setIsProcessing(false);\n            } else if (response.processing_status === 'failed') {\n                setIsProcessing(false);\n            // Could show an error message here\n            } else {\n                // Still processing, check again in 2 seconds\n                setTimeout(()=>checkDocumentStatus(documentId), 2000);\n            }\n        } catch (error) {\n            console.error('Error checking document status:', error);\n            setIsProcessing(false);\n        }\n    };\n    const renderInputComponent = ()=>{\n        // Check if we have a document ID but no uploaded data yet\n        const documentId = searchParams.get(\"documentId\");\n        if (documentId && !uploadedData && !isProcessing) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 h-full flex flex-col items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"rounded-lg p-8 text-center \".concat(theme === \"light\" ? \"bg-white border border-black\" : \"bg-neutral-800\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg mb-2\",\n                            children: \"Loading document...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm \".concat(theme === \"light\" ? \"text-neutral-600\" : \"text-neutral-500\"),\n                            children: \"Please wait while we load your document for preview.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                lineNumber: 185,\n                columnNumber: 9\n            }, this);\n        }\n        if (uploadedData) {\n            if (uploadedData.type === \"file\") {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 h-full flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold mb-4\",\n                            children: uploadedData.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_file_preview__WEBPACK_IMPORTED_MODULE_18__.FilePreview, {\n                                documentId: uploadedData.documentId,\n                                fileName: uploadedData.name,\n                                fileType: uploadedData.fileType,\n                                className: \"h-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 11\n                }, this);\n            } else if (uploadedData.type === \"text\") {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 h-full flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold mb-4\",\n                            children: \"Pasted Content\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"rounded-lg p-4 \".concat(theme === \"light\" ? \"bg-white border border-black\" : \"bg-neutral-800\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: uploadedData.content\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 11\n                }, this);\n            } else if (uploadedData.type === \"audio\") {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold mb-4\",\n                            children: uploadedData.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"rounded-lg p-4 flex flex-col items-center \".concat(theme === \"light\" ? \"bg-white border border-black\" : \"bg-neutral-800\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full h-24 rounded-md mb-4 \".concat(theme === \"light\" ? \"bg-gray-200\" : \"bg-neutral-700\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                                    controls: true,\n                                    className: \"w-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                        src: \"#\",\n                                        type: \"audio/wav\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm mt-2 \".concat(theme === \"light\" ? \"text-neutral-600\" : \"text-neutral-500\"),\n                                    children: [\n                                        \"Duration: \",\n                                        uploadedData.duration\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 11\n                }, this);\n            }\n        }\n        switch(type){\n            case \"upload\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_upload_content__WEBPACK_IMPORTED_MODULE_5__.UploadContent, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 29\n                }, this);\n            case \"paste\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_paste_content__WEBPACK_IMPORTED_MODULE_6__.PasteContent, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 28\n                }, this);\n            case \"record\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_record_content__WEBPACK_IMPORTED_MODULE_7__.RecordContent, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 29\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_upload_content__WEBPACK_IMPORTED_MODULE_5__.UploadContent, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    const renderOutputComponent = ()=>{\n        // Try to get document ID from URL params first, then from localStorage\n        let documentId = searchParams.get(\"documentId\") ? parseInt(searchParams.get(\"documentId\")) : undefined;\n        if (!documentId) {\n            const storedDocumentId = localStorage.getItem(\"currentDocumentId\");\n            if (storedDocumentId) {\n                documentId = parseInt(storedDocumentId);\n            }\n        }\n        switch(activeTab){\n            case \"chat\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_interface__WEBPACK_IMPORTED_MODULE_8__.ChatInterface, {\n                    state: chatState,\n                    setState: setChatState,\n                    documentId: documentId\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 264,\n                    columnNumber: 16\n                }, this);\n            case \"summary\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_summary_interface__WEBPACK_IMPORTED_MODULE_15__.SummaryInterface, {\n                    documentId: documentId\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 266,\n                    columnNumber: 16\n                }, this);\n            case \"flowchart\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_flowchart_interface__WEBPACK_IMPORTED_MODULE_13__.FlowchartInterface, {\n                    documentId: documentId\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 268,\n                    columnNumber: 16\n                }, this);\n            case \"flashcards\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_flashcards_interface__WEBPACK_IMPORTED_MODULE_14__.FlashcardsInterface, {\n                    documentId: documentId\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 270,\n                    columnNumber: 16\n                }, this);\n            case \"chapters\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chapters_interface__WEBPACK_IMPORTED_MODULE_16__.ChaptersInterface, {\n                    documentId: documentId\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 272,\n                    columnNumber: 16\n                }, this);\n            case \"transcript\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_performance_dashboard__WEBPACK_IMPORTED_MODULE_9__.PerformanceDashboard, {\n                    documentId: documentId\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 274,\n                    columnNumber: 16\n                }, this);\n            case \"blueprint\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_blueprint_interface__WEBPACK_IMPORTED_MODULE_12__.BlueprintInterface, {\n                    documentId: documentId\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 276,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_interface__WEBPACK_IMPORTED_MODULE_8__.ChatInterface, {\n                    state: chatState,\n                    setState: setChatState,\n                    documentId: documentId\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 278,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    // Enhanced document time tracking\n    const documentId = searchParams.get(\"documentId\") ? parseInt(searchParams.get(\"documentId\")) : undefined;\n    const { isActive, isPaused, sessionStartTime, endSession } = (0,_hooks_use_document_time__WEBPACK_IMPORTED_MODULE_20__.useDocumentTime)({\n        documentId: documentId,\n        enabled: !!documentId,\n        isProcessingComplete: !isProcessing\n    });\n    // Handle navigation away from process page\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProcessPage.useEffect\": ()=>{\n            const handleRouteChange = {\n                \"ProcessPage.useEffect.handleRouteChange\": ()=>{\n                    // End session when navigating away from process page\n                    if (isActive && endSession) {\n                        console.log('Navigating away from process page, ending session');\n                        endSession();\n                    }\n                }\n            }[\"ProcessPage.useEffect.handleRouteChange\"];\n            // Listen for route changes using Next.js router\n            const originalPush = router.push;\n            const originalReplace = router.replace;\n            const originalBack = router.back;\n            router.push = ({\n                \"ProcessPage.useEffect\": function() {\n                    for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n                        args[_key] = arguments[_key];\n                    }\n                    handleRouteChange();\n                    return originalPush.apply(router, args);\n                }\n            })[\"ProcessPage.useEffect\"];\n            router.replace = ({\n                \"ProcessPage.useEffect\": function() {\n                    for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n                        args[_key] = arguments[_key];\n                    }\n                    handleRouteChange();\n                    return originalReplace.apply(router, args);\n                }\n            })[\"ProcessPage.useEffect\"];\n            router.back = ({\n                \"ProcessPage.useEffect\": function() {\n                    for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n                        args[_key] = arguments[_key];\n                    }\n                    handleRouteChange();\n                    return originalBack.apply(router, args);\n                }\n            })[\"ProcessPage.useEffect\"];\n            // Cleanup\n            return ({\n                \"ProcessPage.useEffect\": ()=>{\n                    router.push = originalPush;\n                    router.replace = originalReplace;\n                    router.back = originalBack;\n                }\n            })[\"ProcessPage.useEffect\"];\n        }\n    }[\"ProcessPage.useEffect\"], [\n        router,\n        isActive,\n        endSession\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_with_sidebar__WEBPACK_IMPORTED_MODULE_10__.LayoutWithSidebar, {\n        showQuizButton: true,\n        showUpgradeButton: true,\n        documentId: documentId,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_loading_overlay__WEBPACK_IMPORTED_MODULE_17__.LoadingOverlay, {\n                isVisible: isProcessing,\n                message: \"Processing your document. This may take a few moments...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                lineNumber: 335,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_22__.motion.div, {\n                className: \"h-[calc(100vh-65px)] overflow-hidden bg-background text-foreground flex flex-col\",\n                initial: {\n                    opacity: 0\n                },\n                animate: {\n                    opacity: 1\n                },\n                transition: {\n                    duration: 0.3\n                },\n                children: showFullScreen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_22__.motion.div, {\n                    className: \"flex-1 p-4\",\n                    children: renderInputComponent()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 344,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_resizable__WEBPACK_IMPORTED_MODULE_4__.ResizablePanelGroup, {\n                    direction: \"horizontal\",\n                    className: \"flex-1 overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_resizable__WEBPACK_IMPORTED_MODULE_4__.ResizablePanel, {\n                            defaultSize: 40,\n                            minSize: 30,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full overflow-auto\",\n                                children: renderInputComponent()\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                            lineNumber: 347,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_resizable__WEBPACK_IMPORTED_MODULE_4__.ResizableHandle, {\n                            withHandle: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                            lineNumber: 350,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_resizable__WEBPACK_IMPORTED_MODULE_4__.ResizablePanel, {\n                            defaultSize: 60,\n                            minSize: 30,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full flex flex-col\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.Tabs, {\n                                    defaultValue: \"chat\",\n                                    onValueChange: setActiveTab,\n                                    value: activeTab,\n                                    className: \"w-full h-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-b border-border sticky top-0 bg-background z-10\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-4 overflow-x-auto scrollbar-hide flex items-center justify-between\",\n                                                ref: tabsListRef,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsList, {\n                                                        className: \"h-12 w-max\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                                                value: \"chat\",\n                                                                className: \"gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart4_BookOpen_FileLineChartIcon_FileText_FlashlightIcon_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                                        lineNumber: 358,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Chat\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                                lineNumber: 357,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                                                value: \"blueprint\",\n                                                                className: \"gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart4_BookOpen_FileLineChartIcon_FileText_FlashlightIcon_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                                        lineNumber: 361,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Blueprint\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                                lineNumber: 360,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                                                value: \"summary\",\n                                                                className: \"gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart4_BookOpen_FileLineChartIcon_FileText_FlashlightIcon_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                                        lineNumber: 364,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Summary\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                                lineNumber: 363,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                                                value: \"flowchart\",\n                                                                className: \"gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart4_BookOpen_FileLineChartIcon_FileText_FlashlightIcon_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                                        lineNumber: 367,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Flowchart\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                                lineNumber: 366,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                                                value: \"flashcards\",\n                                                                className: \"gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart4_BookOpen_FileLineChartIcon_FileText_FlashlightIcon_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                                        lineNumber: 370,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Flashcards\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                                lineNumber: 369,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                                                value: \"chapters\",\n                                                                className: \"gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart4_BookOpen_FileLineChartIcon_FileText_FlashlightIcon_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                                        lineNumber: 373,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Chapters\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                                lineNumber: 372,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                                                value: \"transcript\",\n                                                                className: \"gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart4_BookOpen_FileLineChartIcon_FileText_FlashlightIcon_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                                        lineNumber: 376,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Performance\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                                lineNumber: 375,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 ml-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_session_time_display__WEBPACK_IMPORTED_MODULE_19__.SessionTimeDisplay, {\n                                                            isActive: isActive,\n                                                            isPaused: isPaused,\n                                                            sessionStartTime: sessionStartTime,\n                                                            className: \"text-muted-foreground\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                            lineNumber: 380,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                            value: activeTab,\n                                            className: \"flex-1 p-0 m-0 overflow-hidden h-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-full overflow-y-auto pb-8 scrollbar-thin scrollbar-thumb-neutral-500 scrollbar-track-transparent hover:scrollbar-thumb-neutral-400\",\n                                                children: renderOutputComponent()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                lineNumber: 391,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                    lineNumber: 353,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 346,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                lineNumber: 339,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n        lineNumber: 330,\n        columnNumber: 5\n    }, this);\n}\n_s(ProcessPage, \"PAfjR31IcKrXL6wXq1tIJcUMBGI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _components_theme_provider__WEBPACK_IMPORTED_MODULE_11__.useTheme,\n        _hooks_use_document_time__WEBPACK_IMPORTED_MODULE_20__.useDocumentTime\n    ];\n});\n_c = ProcessPage;\nvar _c;\n$RefreshReg$(_c, \"ProcessPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/process/page.tsx\n"));

/***/ })

});