# Generated by Django 4.2.21 on 2025-06-25 09:13

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('documents', '0010_rename_questionanswer_quiz'),
        ('users', '0007_alter_studentperformance_options_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='DocumentTimeSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_start', models.DateTimeField(help_text='When the session started (Indian timezone)')),
                ('session_end', models.DateTimeField(blank=True, help_text='When the session ended (Indian timezone)', null=True)),
                ('total_time_seconds', models.PositiveIntegerField(default=0, help_text='Total time spent in seconds')),
                ('status', models.CharField(choices=[('active', 'Active'), ('paused', 'Paused'), ('completed', 'Completed'), ('abandoned', 'Abandoned')], default='active', max_length=20)),
                ('last_activity', models.DateTimeField(auto_now=True, help_text='Last recorded activity (for heartbeat tracking)')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('document', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='time_sessions', to='documents.document')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='document_sessions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Document Time Session',
                'verbose_name_plural': 'Document Time Sessions',
                'ordering': ['-session_start'],
            },
        ),
        migrations.CreateModel(
            name='DocumentTimeInterval',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('interval_type', models.CharField(choices=[('study', 'Study Time'), ('quiz_pause', 'Quiz Pause'), ('break', 'Break'), ('idle', 'Idle Time')], default='study', max_length=20)),
                ('start_time', models.DateTimeField(help_text='Start time of this interval (Indian timezone)')),
                ('end_time', models.DateTimeField(blank=True, help_text='End time of this interval (Indian timezone)', null=True)),
                ('duration_seconds', models.PositiveIntegerField(default=0, help_text='Duration of this interval in seconds')),
                ('notes', models.TextField(blank=True, help_text='Additional notes about this interval', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('session', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='intervals', to='users.documenttimesession')),
            ],
            options={
                'verbose_name': 'Document Time Interval',
                'verbose_name_plural': 'Document Time Intervals',
                'ordering': ['start_time'],
            },
        ),
        migrations.CreateModel(
            name='DocumentTimeStats',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_study_time_seconds', models.PositiveIntegerField(default=0, help_text='Total active study time in seconds')),
                ('total_sessions', models.PositiveIntegerField(default=0, help_text='Total number of sessions')),
                ('total_quiz_pauses', models.PositiveIntegerField(default=0, help_text='Number of times quiz was taken')),
                ('average_session_duration', models.FloatField(default=0.0, help_text='Average session duration in seconds')),
                ('first_access', models.DateTimeField(blank=True, help_text='First time document was accessed', null=True)),
                ('last_access', models.DateTimeField(blank=True, help_text='Last time document was accessed', null=True)),
                ('view_count', models.PositiveIntegerField(default=0, help_text='Number of times document was viewed')),
                ('reopened_at_least_once', models.BooleanField(default=False, help_text='True if view_count > 1')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('document', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='time_stats', to='documents.document')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='document_time_stats', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Document Time Statistics',
                'verbose_name_plural': 'Document Time Statistics',
                'ordering': ['-last_access'],
                'indexes': [models.Index(fields=['student', 'document'], name='users_docum_student_baedfc_idx'), models.Index(fields=['total_study_time_seconds'], name='users_docum_total_s_df3d4c_idx'), models.Index(fields=['view_count'], name='users_docum_view_co_58b28f_idx'), models.Index(fields=['last_access'], name='users_docum_last_ac_9a2740_idx')],
                'unique_together': {('student', 'document')},
            },
        ),
        migrations.AddIndex(
            model_name='documenttimesession',
            index=models.Index(fields=['student', 'document'], name='users_docum_student_f76c89_idx'),
        ),
        migrations.AddIndex(
            model_name='documenttimesession',
            index=models.Index(fields=['status'], name='users_docum_status_4916f4_idx'),
        ),
        migrations.AddIndex(
            model_name='documenttimesession',
            index=models.Index(fields=['session_start'], name='users_docum_session_b5cfa5_idx'),
        ),
        migrations.AddIndex(
            model_name='documenttimesession',
            index=models.Index(fields=['last_activity'], name='users_docum_last_ac_f4ff0d_idx'),
        ),
        migrations.AddIndex(
            model_name='documenttimeinterval',
            index=models.Index(fields=['session', 'interval_type'], name='users_docum_session_ad4beb_idx'),
        ),
        migrations.AddIndex(
            model_name='documenttimeinterval',
            index=models.Index(fields=['start_time'], name='users_docum_start_t_768e19_idx'),
        ),
        migrations.AddIndex(
            model_name='documenttimeinterval',
            index=models.Index(fields=['interval_type'], name='users_docum_interva_487822_idx'),
        ),
    ]
