#!/usr/bin/env python3
"""
Test script to directly test Gemini learning time prediction.

This script tests the FastAPI endpoint directly to verify that:
1. Gemini API is working
2. Learning time prediction logic is correct
3. Results are saved to the backend
"""

import requests
import json
import os
import sys
import django

# Add the core directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'core'))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from documents.models import Document, DocumentLearningTime, DocumentEmbedding
from users.models import Student


def create_test_document():
    """Create a test document with realistic content"""
    
    # Create test student
    student, created = Student.objects.get_or_create(
        username='test_gemini_predict',
        defaults={'email': '<EMAIL>'}
    )
    
    # Create test document
    document = Document.objects.create(
        user=student,
        title='Advanced Mathematics - Integration Techniques',
        file='integration_techniques.pdf',
        processing_status='completed'
    )
    
    # Create realistic educational content for testing
    test_chunks = [
        "Integration by Parts: The integration by parts formula is ∫u dv = uv - ∫v du. This technique is particularly useful when integrating products of functions.",
        
        "Example 1: Find ∫x·e^x dx. Let u = x and dv = e^x dx. Then du = dx and v = e^x. Using integration by parts: ∫x·e^x dx = x·e^x - ∫e^x dx = x·e^x - e^x + C = e^x(x-1) + C",
        
        "Trigonometric Substitution: This method is used for integrals involving √(a²-x²), √(a²+x²), or √(x²-a²). We substitute x = a·sin(θ), x = a·tan(θ), or x = a·sec(θ) respectively.",
        
        "Example 2: Find ∫√(4-x²) dx. Use the substitution x = 2sin(θ), dx = 2cos(θ)dθ. The integral becomes ∫2cos(θ)·2cos(θ)dθ = 4∫cos²(θ)dθ",
        
        "Partial Fractions: This technique decomposes rational functions into simpler fractions. For f(x)/g(x) where degree of f < degree of g, we write it as a sum of simpler fractions.",
        
        "Practice Problems: 1) ∫x²·ln(x) dx, 2) ∫√(9+x²) dx, 3) ∫(x+1)/(x²+5x+6) dx. These problems require mastery of the techniques and typically take 15-20 minutes each to solve."
    ]
    
    # Create embeddings
    for i, chunk in enumerate(test_chunks):
        DocumentEmbedding.objects.create(
            document=document,
            text_chunk=chunk,
            embedding=[0.1] * 384,  # Dummy embedding
            chunk_number=i
        )
    
    return document


def test_fastapi_prediction(document_id):
    """Test the FastAPI learning time prediction endpoint"""
    
    print(f"Testing FastAPI learning time prediction for document {document_id}")
    print("=" * 60)
    
    # FastAPI endpoint URL
    url = f"http://localhost:8001/predict-learning-time/{document_id}"
    
    # Create request payload
    payload = {
        "document_id": document_id,
        "llm_model": "gemini",
        "force_regenerate": True  # Force new prediction for testing
    }
    
    # Create dummy auth token
    headers = {
        "Authorization": "Bearer test_user_token",
        "Content-Type": "application/json"
    }
    
    try:
        print("Sending request to FastAPI...")
        response = requests.post(url, json=payload, headers=headers)
        
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✓ FastAPI prediction successful!")
            print(f"  - Message: {data['message']}")
            print(f"  - Document ID: {data['document_id']}")
            print(f"  - Predicted time: {data['predicted_time_seconds']} seconds")
            print(f"  - Time range: {data['time_range_display']}")
            print(f"  - Topic difficulty: {data['analysis']['topic_difficulty']}")
            print(f"  - Content length: {data['analysis']['content_length_words']} words")
            print(f"  - Concept density: {data['analysis']['concept_density']}")
            print(f"  - Additional factors: {data['analysis']['additional_factors']}")
            print(f"  - Model used: {data['model']}")
            print(f"  - Tokens used: {data['tokens']}")
            print(f"  - Gemini reasoning: {data['gemini_reasoning'][:200]}...")
            
            return True
        else:
            print(f"✗ FastAPI prediction failed: {response.text}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("✗ Cannot connect to FastAPI server")
        print("Make sure FastAPI server is running on localhost:8001")
        return False
    except Exception as e:
        print(f"✗ Error testing FastAPI: {str(e)}")
        return False


def verify_backend_storage(document_id):
    """Verify that the prediction was saved to the backend"""
    
    print(f"\nVerifying backend storage for document {document_id}")
    print("=" * 50)
    
    try:
        document = Document.objects.get(id=document_id)
        learning_time = DocumentLearningTime.objects.filter(document=document).first()
        
        if learning_time:
            print("✓ Learning time prediction found in backend!")
            print(f"  - Document: {learning_time.document.title}")
            print(f"  - Predicted time: {learning_time.predicted_time_seconds} seconds")
            print(f"  - Minutes: {learning_time.predicted_time_seconds // 60}")
            print(f"  - Time range: {learning_time.get_time_range_display()}")
            print(f"  - Difficulty: {learning_time.get_topic_difficulty_display()}")
            print(f"  - Concept density: {learning_time.get_concept_density_display()}")
            print(f"  - Content words: {learning_time.content_length_words}")
            print(f"  - Created: {learning_time.created_at}")
            print(f"  - Analysis factors: {learning_time.analysis_factors}")
            
            return True
        else:
            print("✗ No learning time prediction found in backend")
            return False
            
    except Document.DoesNotExist:
        print(f"✗ Document {document_id} not found")
        return False
    except Exception as e:
        print(f"✗ Error checking backend: {str(e)}")
        return False


def main():
    """Main test function"""
    print("Gemini Learning Time Prediction Test")
    print("=" * 60)
    
    # Create test document
    document = create_test_document()
    print(f"✓ Created test document: {document.title} (ID: {document.id})")
    print(f"✓ Document has {document.embeddings.count()} content chunks")
    
    # Test FastAPI prediction
    fastapi_success = test_fastapi_prediction(document.id)
    
    # Verify backend storage
    backend_success = verify_backend_storage(document.id)
    
    # Summary
    print("\n" + "=" * 60)
    print("Test Results Summary:")
    
    if fastapi_success and backend_success:
        print("🎉 SUCCESS: Complete learning time prediction workflow working!")
        print("✓ Gemini AI is generating predictions")
        print("✓ FastAPI endpoint is working")
        print("✓ Predictions are saved to backend")
        print("✓ All factors are being considered")
        
        print("\n🎯 The system is working perfectly!")
        print("When you upload files through your frontend:")
        print("1. Document gets processed")
        print("2. Gemini automatically predicts learning time")
        print("3. Prediction gets saved to backend")
        print("4. You can view it in Django admin")
        
    elif fastapi_success:
        print("⚠️  PARTIAL: FastAPI working but backend storage issue")
        print("✓ Gemini AI is generating predictions")
        print("✓ FastAPI endpoint is working")
        print("✗ Backend storage needs checking")
        
    elif backend_success:
        print("⚠️  PARTIAL: Backend working but FastAPI issue")
        print("✗ FastAPI endpoint needs checking")
        print("✓ Backend storage is working")
        
    else:
        print("❌ FAILED: Both FastAPI and backend need attention")
        print("Check that:")
        print("1. FastAPI server is running on localhost:8001")
        print("2. Gemini API key is configured")
        print("3. Django database is accessible")
    
    print(f"\nTotal learning time predictions in database: {DocumentLearningTime.objects.count()}")


if __name__ == '__main__':
    main()
