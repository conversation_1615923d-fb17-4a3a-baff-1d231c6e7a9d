import { renderHook, act } from '@testing-library/react';
import { useTimeTracking } from '@/hooks/use-time-tracking';
import { timeTrackingApi } from '@/lib/api';

// Mock the API
jest.mock('@/lib/api', () => ({
  timeTrackingApi: {
    startSession: jest.fn(),
    updateActivity: jest.fn(),
    pauseSession: jest.fn(),
    resumeSession: jest.fn(),
    endSession: jest.fn(),
    getDocumentStats: jest.fn(),
    getUserOverview: jest.fn(),
  },
}));

// Mock localStorage
const mockLocalStorage = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
};
Object.defineProperty(window, 'localStorage', {
  value: mockLocalStorage,
});

// Mock toast
jest.mock('@/hooks/use-toast', () => ({
  toast: jest.fn(),
}));

describe('useTimeTracking Hook', () => {
  const mockDocumentId = 123;
  
  beforeEach(() => {
    jest.clearAllMocks();
    mockLocalStorage.getItem.mockReturnValue('mock-token');
    
    // Mock successful API responses
    (timeTrackingApi.startSession as jest.Mock).mockResolvedValue({
      session_id: 'session-123',
      status: 'active',
      start_time: new Date().toISOString(),
    });
    
    (timeTrackingApi.updateActivity as jest.Mock).mockResolvedValue({});
    (timeTrackingApi.endSession as jest.Mock).mockResolvedValue({
      total_time_seconds: 300,
      total_time_formatted: '5:00',
    });
  });

  it('should initialize with default state', () => {
    const { result } = renderHook(() => 
      useTimeTracking({ documentId: mockDocumentId, autoStart: false })
    );

    expect(result.current.isTracking).toBe(false);
    expect(result.current.totalTime).toBe('0:00');
    expect(result.current.sessionId).toBeNull();
    expect(result.current.status).toBe('stopped');
  });

  it('should start tracking when startTracking is called', async () => {
    const { result } = renderHook(() => 
      useTimeTracking({ documentId: mockDocumentId, autoStart: false })
    );

    await act(async () => {
      await result.current.startTracking();
    });

    expect(timeTrackingApi.startSession).toHaveBeenCalledWith({
      document: mockDocumentId,
    });
    expect(result.current.isTracking).toBe(true);
    expect(result.current.status).toBe('active');
  });

  it('should auto-start when autoStart is true', () => {
    renderHook(() => 
      useTimeTracking({ documentId: mockDocumentId, autoStart: true })
    );

    expect(timeTrackingApi.startSession).toHaveBeenCalledWith({
      document: mockDocumentId,
    });
  });

  it('should stop tracking when stopTracking is called', async () => {
    const { result } = renderHook(() => 
      useTimeTracking({ documentId: mockDocumentId, autoStart: false })
    );

    // Start tracking first
    await act(async () => {
      await result.current.startTracking();
    });

    // Then stop tracking
    await act(async () => {
      await result.current.stopTracking();
    });

    expect(timeTrackingApi.endSession).toHaveBeenCalled();
    expect(result.current.isTracking).toBe(false);
    expect(result.current.status).toBe('stopped');
  });

  it('should pause and resume tracking', async () => {
    const { result } = renderHook(() => 
      useTimeTracking({ documentId: mockDocumentId, autoStart: false })
    );

    // Start tracking
    await act(async () => {
      await result.current.startTracking();
    });

    // Pause tracking
    await act(async () => {
      await result.current.pauseTracking('Test pause');
    });

    expect(timeTrackingApi.pauseSession).toHaveBeenCalled();
    expect(result.current.status).toBe('paused');

    // Resume tracking
    await act(async () => {
      await result.current.resumeTracking();
    });

    expect(timeTrackingApi.resumeSession).toHaveBeenCalled();
    expect(result.current.status).toBe('active');
  });

  it('should handle API errors gracefully', async () => {
    (timeTrackingApi.startSession as jest.Mock).mockRejectedValue(
      new Error('API Error')
    );

    const onError = jest.fn();
    const { result } = renderHook(() => 
      useTimeTracking({ 
        documentId: mockDocumentId, 
        autoStart: false,
        onError 
      })
    );

    await act(async () => {
      await result.current.startTracking();
    });

    expect(onError).toHaveBeenCalledWith(expect.any(Error));
    expect(result.current.isTracking).toBe(false);
  });

  it('should call lifecycle callbacks', async () => {
    const onSessionStart = jest.fn();
    const onSessionEnd = jest.fn();

    const { result } = renderHook(() => 
      useTimeTracking({ 
        documentId: mockDocumentId, 
        autoStart: false,
        onSessionStart,
        onSessionEnd
      })
    );

    // Start session
    await act(async () => {
      await result.current.startTracking();
    });

    expect(onSessionStart).toHaveBeenCalled();

    // End session
    await act(async () => {
      await result.current.stopTracking();
    });

    expect(onSessionEnd).toHaveBeenCalled();
  });

  it('should update timer display', async () => {
    jest.useFakeTimers();
    
    const { result } = renderHook(() => 
      useTimeTracking({ documentId: mockDocumentId, autoStart: false })
    );

    await act(async () => {
      await result.current.startTracking();
    });

    // Fast-forward time
    act(() => {
      jest.advanceTimersByTime(65000); // 1 minute 5 seconds
    });

    expect(result.current.totalTime).toBe('1:05');

    jest.useRealTimers();
  });

  it('should cleanup on unmount', () => {
    const { unmount } = renderHook(() => 
      useTimeTracking({ documentId: mockDocumentId, autoStart: true })
    );

    unmount();

    // Should call endSession when component unmounts
    expect(timeTrackingApi.endSession).toHaveBeenCalled();
  });
});

describe('Time Tracking Integration', () => {
  it('should handle page visibility changes', () => {
    const { result } = renderHook(() => 
      useTimeTracking({ documentId: mockDocumentId, autoStart: true })
    );

    // Simulate page becoming hidden
    Object.defineProperty(document, 'hidden', {
      writable: true,
      value: true,
    });

    const visibilityEvent = new Event('visibilitychange');
    document.dispatchEvent(visibilityEvent);

    // Should pause heartbeat when page is hidden
    expect(result.current.status).toBe('active'); // Still active but heartbeat paused
  });

  it('should handle beforeunload event', () => {
    renderHook(() => 
      useTimeTracking({ documentId: mockDocumentId, autoStart: true })
    );

    // Simulate page unload
    const beforeUnloadEvent = new Event('beforeunload');
    window.dispatchEvent(beforeUnloadEvent);

    // Should attempt to end session
    expect(timeTrackingApi.endSession).toHaveBeenCalled();
  });
});
