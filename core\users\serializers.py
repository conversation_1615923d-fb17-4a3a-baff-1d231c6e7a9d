import requests
from google.oauth2 import id_token
from rest_framework import serializers
from django.core.mail import send_mail
from django.conf import settings
from django.urls import reverse
from django.contrib.auth.models import User
from .models import Student, UserProfile, UserUsage, StudentPerformance, PlatformTimeSession, PlatformTimeStats, PlatformTimeEvent

class StudentSerializer(serializers.ModelSerializer):
    class Meta:
        model = Student
        fields = '__all__'

class StudentRegistrationSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True)
    confirm_password = serializers.CharField(write_only=True)

    class Meta:
        model = Student
        fields = ('email', 'username', 'password', 'confirm_password', 'first_name', 'last_name')

    def validate(self, data):
        if data['password'] != data['confirm_password']:
            raise serializers.ValidationError("Passwords do not match")
        return data

    def create(self, validated_data):
        validated_data.pop('confirm_password')
        # Create an inactive user
        student = Student.objects.create_user(
            username=validated_data.pop('username'),  # Extract username first
            is_active=False,
            **validated_data
        )

        # Generate and send OTP
        otp = student.generate_otp()

        send_mail(
            'Your OTP for registration',
            f'Your OTP for registration is: {otp}\nThis OTP is valid for 10 minutes.',
            settings.DEFAULT_FROM_EMAIL,
            [student.email],
            fail_silently=False,
        )

        return student

class OTPVerificationSerializer(serializers.Serializer):
    email = serializers.EmailField()
    otp = serializers.CharField(min_length=6, max_length=6)

    def validate(self, data):
        try:
            student = Student.objects.get(email=data['email'])
            if student.is_active:
                raise serializers.ValidationError("Account is already verified")
            if not student.verify_otp(data['otp']):
                raise serializers.ValidationError("Invalid or expired OTP")
            return data
        except Student.DoesNotExist:
            raise serializers.ValidationError("Invalid email")

class EmailVerificationSerializer(serializers.Serializer):
    token = serializers.CharField()

    def validate_token(self, value):
        try:
            student = Student.objects.get(email_verification_token=value)
            if student.is_email_verified:
                raise serializers.ValidationError("Email already verified")
            return value
        except Student.DoesNotExist:
            raise serializers.ValidationError("Invalid verification token")

class UserProfileSerializer(serializers.ModelSerializer):
    class Meta:
        model = UserProfile
        fields = ['is_paid', 'created_at', 'updated_at']

class UserUsageSerializer(serializers.ModelSerializer):
    chat_limit = serializers.SerializerMethodField()
    file_upload_limit = serializers.SerializerMethodField()
    remaining_chats = serializers.SerializerMethodField()
    remaining_file_uploads = serializers.SerializerMethodField()

    class Meta:
        model = UserUsage
        fields = ['date', 'chat_count', 'file_upload_count', 'chat_limit',
                 'file_upload_limit', 'remaining_chats', 'remaining_file_uploads']

    def get_chat_limit(self, obj):
        return obj.chat_limit

    def get_file_upload_limit(self, obj):
        return obj.file_upload_limit

    def get_remaining_chats(self, obj):
        return obj.chat_limit - obj.chat_count

    def get_remaining_file_uploads(self, obj):
        return obj.file_upload_limit - obj.file_upload_count

class GoogleAuthSerializer(serializers.Serializer):
    token = serializers.CharField(required=True)

    def validate_token(self, token):
        try:
            idinfo = id_token.verify_oauth2_token(
                token,
                requests.Request(),
                settings.GOOGLE_OAUTH2_CLIENT_ID
            )
            return idinfo
        except ValueError:
            raise serializers.ValidationError("Invalid token")


class StudentPerformanceSerializer(serializers.ModelSerializer):
    document_title = serializers.CharField(source='document.title', read_only=True)
    student_name = serializers.CharField(source='student.username', read_only=True)

    class Meta:
        model = StudentPerformance
        fields = ['id', 'student', 'document', 'quiz_score', 'time_taken', 'remarks',
                 'created_at', 'document_title', 'student_name']
        read_only_fields = ['created_at']


class PlatformTimeSessionSerializer(serializers.ModelSerializer):
    document_title = serializers.CharField(source='document.title', read_only=True)
    student_name = serializers.CharField(source='student.username', read_only=True)

    class Meta:
        model = PlatformTimeSession
        fields = ['id', 'student', 'document', 'session_start', 'session_end',
                 'total_time_seconds', 'is_active', 'is_paused', 'pause_reason',
                 'last_activity', 'created_at', 'document_title', 'student_name']
        read_only_fields = ['created_at', 'last_activity']


class PlatformTimeEventSerializer(serializers.ModelSerializer):
    session_id = serializers.IntegerField(source='session.id', read_only=True)
    document_title = serializers.CharField(source='session.document.title', read_only=True)
    student_name = serializers.CharField(source='session.student.username', read_only=True)

    class Meta:
        model = PlatformTimeEvent
        fields = ['id', 'session_id', 'event_type', 'reason', 'timestamp',
                 'created_at', 'document_title', 'student_name']
        read_only_fields = ['created_at']


class PlatformTimeStatsSerializer(serializers.ModelSerializer):
    document_title = serializers.CharField(source='document.title', read_only=True)
    student_name = serializers.CharField(source='student.username', read_only=True)
    average_session_time = serializers.ReadOnlyField()
    was_reopened = serializers.ReadOnlyField()

    class Meta:
        model = PlatformTimeStats
        fields = ['id', 'student', 'document', 'total_time_seconds',
                 'total_sessions', 'total_pauses', 'quiz_pauses', 'navigation_pauses',
                 'view_count', 'first_access', 'last_access', 'average_session_time',
                 'was_reopened', 'created_at', 'updated_at', 'document_title', 'student_name']
        read_only_fields = ['created_at', 'updated_at']