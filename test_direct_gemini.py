#!/usr/bin/env python3
"""
Direct test of Gemini learning time prediction logic.

This script tests the core Gemini prediction functionality without FastAPI,
directly using the Gemini API to predict learning time.
"""

import os
import sys
import django

# Add the core directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'core'))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from documents.models import Document, DocumentLearningTime, DocumentEmbedding
from users.models import Student
import google.generativeai as genai
import json
import re


def setup_gemini():
    """Setup Gemini API"""
    try:
        # Configure Gemini API
        api_key = os.getenv('GOOGLE_API_KEY')
        if not api_key:
            print("❌ GOOGLE_API_KEY environment variable not set")
            return None
            
        genai.configure(api_key=api_key)
        model = genai.GenerativeModel('gemini-1.5-flash')
        print("✓ Gemini API configured successfully")
        return model
    except Exception as e:
        print(f"❌ Error setting up Gemini: {str(e)}")
        return None


def create_test_document():
    """Create a test document with educational content"""
    
    # Create test student
    student, created = Student.objects.get_or_create(
        username='test_direct_gemini',
        defaults={'email': '<EMAIL>'}
    )
    
    # Create test document
    document = Document.objects.create(
        user=student,
        title='Probability Theory and Statistics',
        file='probability_statistics.pdf',
        processing_status='completed'
    )
    
    # Create realistic educational content
    test_content = """
    Probability Theory and Statistics - Chapter 3: Discrete Probability Distributions
    
    Introduction to Discrete Random Variables
    A discrete random variable is a function that assigns numerical values to the outcomes of a random experiment.
    The possible values are countable (finite or countably infinite).
    
    Probability Mass Function (PMF)
    For a discrete random variable X, the probability mass function is defined as:
    P(X = x) = probability that X takes the value x
    
    Properties of PMF:
    1. P(X = x) ≥ 0 for all x
    2. Σ P(X = x) = 1 (sum over all possible values)
    
    Expected Value and Variance
    Expected Value: E[X] = Σ x · P(X = x)
    Variance: Var(X) = E[X²] - (E[X])²
    
    Common Discrete Distributions:
    
    1. Bernoulli Distribution
    - Models a single trial with two outcomes (success/failure)
    - P(X = 1) = p, P(X = 0) = 1-p
    - E[X] = p, Var(X) = p(1-p)
    
    2. Binomial Distribution
    - Models n independent Bernoulli trials
    - P(X = k) = C(n,k) · p^k · (1-p)^(n-k)
    - E[X] = np, Var(X) = np(1-p)
    
    3. Poisson Distribution
    - Models rare events occurring in fixed intervals
    - P(X = k) = (λ^k · e^(-λ)) / k!
    - E[X] = λ, Var(X) = λ
    
    Example Problems:
    
    Problem 1: A fair coin is flipped 10 times. What is the probability of getting exactly 6 heads?
    Solution: This follows a binomial distribution with n=10, p=0.5
    P(X = 6) = C(10,6) · (0.5)^6 · (0.5)^4 = 210 · (0.5)^10 = 210/1024 ≈ 0.205
    
    Problem 2: A call center receives an average of 3 calls per minute. What is the probability of receiving exactly 5 calls in a given minute?
    Solution: This follows a Poisson distribution with λ=3
    P(X = 5) = (3^5 · e^(-3)) / 5! = (243 · e^(-3)) / 120 ≈ 0.101
    
    Practice Exercises:
    1. Calculate the expected value and variance for a binomial distribution with n=20, p=0.3
    2. Find P(X ≤ 2) for a Poisson distribution with λ=1.5
    3. A quality control inspector finds that 5% of products are defective. In a batch of 50 products, what is the probability that at most 2 are defective?
    
    This material requires understanding of:
    - Basic probability concepts
    - Combinatorics and factorials
    - Exponential functions
    - Summation notation
    - Problem-solving skills
    
    Typical study time for students aged 16-22: 60-90 minutes depending on mathematical background.
    """
    
    # Create embeddings
    chunks = test_content.split('\n\n')
    for i, chunk in enumerate(chunks):
        if chunk.strip():
            DocumentEmbedding.objects.create(
                document=document,
                text_chunk=chunk.strip(),
                embedding=[0.1] * 384,  # Dummy embedding
                chunk_number=i
            )
    
    return document


def predict_learning_time_with_gemini(document, model):
    """Use Gemini to predict learning time for the document"""
    
    print(f"Predicting learning time for: {document.title}")
    print("=" * 50)
    
    # Get document content
    content_chunks = document.embeddings.all().order_by('chunk_number')
    content = '\n\n'.join([chunk.text_chunk for chunk in content_chunks])
    word_count = len(content.split())
    
    print(f"✓ Document content: {word_count} words")
    
    # Create Gemini prompt
    prompt = f"""
    You are an expert educational analyst specializing in learning time prediction for students aged 16-22.
    
    Analyze the following document content and predict the learning time based on these factors:
    
    1. **Topic Difficulty** (1-5 scale):
       - 1: Very Easy (basic concepts, familiar topics)
       - 2: Easy (simple concepts with some new terminology)
       - 3: Medium (moderate complexity, requires some background knowledge)
       - 4: Hard (complex concepts, significant background knowledge required)
       - 5: Very Hard (advanced concepts, extensive prerequisites)
    
    2. **Content Length**: {word_count} words
    
    3. **Concept Density** (1-5 scale):
       - 1: Low - Basic concepts, mostly descriptive
       - 2: Low-Medium - Some formulas or technical terms
       - 3: Medium - Moderate complexity with examples
       - 4: Medium-High - Formula heavy, requires practice
       - 5: High - Abstract concepts, problem-solving intensive
    
    Consider these additional factors:
    - Mathematical formulas and equations
    - Abstract concepts requiring deep thinking
    - Number of examples and practice problems
    - Prerequisites knowledge required
    - Subject area complexity
    
    Document Content:
    {content}
    
    IMPORTANT: Respond with ONLY valid JSON in this exact format:
    
    {{
        "topic_difficulty": 3,
        "concept_density": 4,
        "predicted_time_seconds": 3600,
        "reasoning": "This document covers intermediate probability theory with multiple formulas and practice problems. The topic difficulty is moderate (3/5) as it requires some mathematical background. The concept density is high (4/5) due to numerous formulas and problem-solving requirements. Based on the content length of {word_count} words and complexity, I estimate 60 minutes for thorough understanding.",
        "additional_factors": {{
            "subject_area": "mathematics",
            "formula_count": 15,
            "example_problems": 8,
            "prerequisite_level": "intermediate"
        }}
    }}
    
    Base your time prediction on these guidelines for age group 16-22:
    - Very Easy topics: 30-60 seconds per 100 words
    - Easy topics: 60-120 seconds per 100 words  
    - Medium topics: 120-240 seconds per 100 words
    - Hard topics: 240-480 seconds per 100 words
    - Very Hard topics: 480-960 seconds per 100 words
    
    Adjust based on concept density and additional factors.
    """
    
    try:
        print("🤖 Sending request to Gemini...")
        response = model.generate_content(prompt)
        response_text = response.text.strip()
        
        print(f"✓ Received response from Gemini ({len(response_text)} characters)")
        
        # Clean and parse JSON response
        response_text = re.sub(r'```json\s*', '', response_text)
        response_text = re.sub(r'```\s*$', '', response_text)
        response_text = response_text.strip()
        
        try:
            analysis_data = json.loads(response_text)
            
            print("✓ Successfully parsed Gemini response")
            print(f"  - Topic difficulty: {analysis_data['topic_difficulty']}/5")
            print(f"  - Concept density: {analysis_data['concept_density']}/5")
            print(f"  - Predicted time: {analysis_data['predicted_time_seconds']} seconds")
            print(f"  - Time in minutes: {analysis_data['predicted_time_seconds'] // 60}")
            print(f"  - Additional factors: {analysis_data['additional_factors']}")
            print(f"  - Reasoning: {analysis_data['reasoning'][:150]}...")
            
            return analysis_data
            
        except (json.JSONDecodeError, KeyError) as e:
            print(f"❌ Failed to parse Gemini response: {str(e)}")
            print(f"Raw response: {response_text[:500]}...")
            return None
            
    except Exception as e:
        print(f"❌ Error calling Gemini API: {str(e)}")
        return None


def save_to_backend(document, analysis_data, word_count):
    """Save the prediction to the backend database"""
    
    print("\nSaving prediction to backend...")
    print("=" * 30)
    
    try:
        learning_time, created = DocumentLearningTime.objects.update_or_create(
            document=document,
            defaults={
                'predicted_time_seconds': analysis_data['predicted_time_seconds'],
                'topic_difficulty': analysis_data['topic_difficulty'],
                'content_length_words': word_count,
                'concept_density': analysis_data['concept_density'],
                'analysis_factors': analysis_data['additional_factors'],
                'gemini_reasoning': analysis_data['reasoning']
            }
        )
        
        print(f"✓ Prediction {'created' if created else 'updated'} in backend")
        print(f"  - Document: {learning_time.document.title}")
        print(f"  - Predicted time: {learning_time.predicted_time_seconds}s")
        print(f"  - Time range: {learning_time.get_time_range_display()}")
        print(f"  - Difficulty: {learning_time.get_topic_difficulty_display()}")
        print(f"  - Concept density: {learning_time.get_concept_density_display()}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error saving to backend: {str(e)}")
        return False


def main():
    """Main test function"""
    print("Direct Gemini Learning Time Prediction Test")
    print("=" * 60)
    
    # Setup Gemini
    model = setup_gemini()
    if not model:
        print("❌ Cannot proceed without Gemini API")
        return
    
    # Create test document
    document = create_test_document()
    print(f"✓ Created test document: {document.title}")
    print(f"✓ Document has {document.embeddings.count()} content chunks")
    
    # Get word count
    content_chunks = document.embeddings.all().order_by('chunk_number')
    content = '\n\n'.join([chunk.text_chunk for chunk in content_chunks])
    word_count = len(content.split())
    
    # Predict learning time
    analysis_data = predict_learning_time_with_gemini(document, model)
    
    if analysis_data:
        # Save to backend
        backend_success = save_to_backend(document, analysis_data, word_count)
        
        if backend_success:
            print("\n🎉 SUCCESS: Complete workflow working!")
            print("✓ Gemini AI generated learning time prediction")
            print("✓ Prediction saved to backend database")
            print("✓ All factors considered (difficulty, density, content length)")
            
            print(f"\nTotal predictions in database: {DocumentLearningTime.objects.count()}")
            print("You can view them in Django admin at: http://localhost:8000/admin/")
            
        else:
            print("\n⚠️  PARTIAL: Gemini working but backend save failed")
    else:
        print("\n❌ FAILED: Gemini prediction failed")
        print("Check your GOOGLE_API_KEY environment variable")


if __name__ == '__main__':
    main()
