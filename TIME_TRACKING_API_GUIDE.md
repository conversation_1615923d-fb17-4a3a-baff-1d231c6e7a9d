# Document Time Tracking API Guide

This guide explains how to use the time tracking API endpoints to track user time spent on documents.

## Overview

The time tracking system consists of three main models:
1. **DocumentTimeSession** - Tracks individual sessions when a user accesses a document
2. **DocumentTimeInterval** - Tracks specific intervals within a session (study time, quiz pauses, etc.)
3. **DocumentTimeStats** - Aggregated statistics per user per document

## API Endpoints

### 1. Start Document Session
**POST** `/users/time-tracking/start-session/`

Start tracking time when user accesses a document.

```javascript
const response = await fetch('/users/time-tracking/start-session/', {
  method: 'POST',
  headers: {
    'Authorization': `Token ${userToken}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    document_id: 123
  })
});

const data = await response.json();
// Returns: { session: {...}, message: "Session started successfully" }
```

### 2. Update Activity (Heartbeat)
**POST** `/users/time-tracking/update-activity/`

Send periodic heartbeats to track user presence (recommended every 30-60 seconds).

```javascript
const updateActivity = async (sessionId) => {
  await fetch('/users/time-tracking/update-activity/', {
    method: 'POST',
    headers: {
      'Authorization': `Token ${userToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      session_id: sessionId
    })
  });
};

// Set up heartbeat
const heartbeatInterval = setInterval(() => {
  if (currentSessionId) {
    updateActivity(currentSessionId);
  }
}, 30000); // Every 30 seconds
```

### 3. Pause/Resume Session
**POST** `/users/time-tracking/pause-resume/`

Pause session when starting a quiz, resume when quiz is done.

```javascript
// Pause when starting quiz
const pauseSession = async (sessionId) => {
  await fetch('/users/time-tracking/pause-resume/', {
    method: 'POST',
    headers: {
      'Authorization': `Token ${userToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      session_id: sessionId,
      action: 'pause',
      notes: 'Starting quiz'
    })
  });
};

// Resume when quiz is done
const resumeSession = async (sessionId) => {
  await fetch('/users/time-tracking/pause-resume/', {
    method: 'POST',
    headers: {
      'Authorization': `Token ${userToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      session_id: sessionId,
      action: 'resume'
    })
  });
};
```

### 4. End Session
**POST** `/users/time-tracking/end-session/`

End session when user leaves the document.

```javascript
const endSession = async (sessionId) => {
  await fetch('/users/time-tracking/end-session/', {
    method: 'POST',
    headers: {
      'Authorization': `Token ${userToken}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      session_id: sessionId
    })
  });
};

// End session on page unload
window.addEventListener('beforeunload', () => {
  if (currentSessionId) {
    // Use sendBeacon for reliable delivery
    navigator.sendBeacon('/users/time-tracking/end-session/', 
      JSON.stringify({ session_id: currentSessionId })
    );
  }
});
```

### 5. Get Document Stats
**GET** `/users/time-tracking/document/{document_id}/stats/`

Get time tracking statistics for a specific document.

```javascript
const getDocumentStats = async (documentId) => {
  const response = await fetch(`/users/time-tracking/document/${documentId}/stats/`, {
    headers: {
      'Authorization': `Token ${userToken}`
    }
  });
  return await response.json();
  // Returns: { stats: {...}, recent_sessions: [...] }
};
```

### 6. Get User Overview
**GET** `/users/time-tracking/overview/`

Get overview of user's time tracking across all documents.

```javascript
const getUserOverview = async () => {
  const response = await fetch('/users/time-tracking/overview/', {
    headers: {
      'Authorization': `Token ${userToken}`
    }
  });
  return await response.json();
  // Returns: { overview: {...}, document_stats: [...], active_sessions: [...] }
};
```

## Frontend Implementation Example

```javascript
class DocumentTimeTracker {
  constructor(userToken) {
    this.userToken = userToken;
    this.currentSessionId = null;
    this.heartbeatInterval = null;
  }

  async startTracking(documentId) {
    try {
      const response = await fetch('/users/time-tracking/start-session/', {
        method: 'POST',
        headers: {
          'Authorization': `Token ${this.userToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ document_id: documentId })
      });
      
      const data = await response.json();
      this.currentSessionId = data.session.id;
      
      // Start heartbeat
      this.startHeartbeat();
      
      return data;
    } catch (error) {
      console.error('Error starting time tracking:', error);
    }
  }

  startHeartbeat() {
    this.heartbeatInterval = setInterval(async () => {
      if (this.currentSessionId) {
        try {
          await fetch('/users/time-tracking/update-activity/', {
            method: 'POST',
            headers: {
              'Authorization': `Token ${this.userToken}`,
              'Content-Type': 'application/json'
            },
            body: JSON.stringify({ session_id: this.currentSessionId })
          });
        } catch (error) {
          console.error('Heartbeat failed:', error);
        }
      }
    }, 30000); // 30 seconds
  }

  async pauseForQuiz() {
    if (!this.currentSessionId) return;
    
    try {
      await fetch('/users/time-tracking/pause-resume/', {
        method: 'POST',
        headers: {
          'Authorization': `Token ${this.userToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          session_id: this.currentSessionId,
          action: 'pause',
          notes: 'Taking quiz'
        })
      });
    } catch (error) {
      console.error('Error pausing session:', error);
    }
  }

  async resumeAfterQuiz() {
    if (!this.currentSessionId) return;
    
    try {
      await fetch('/users/time-tracking/pause-resume/', {
        method: 'POST',
        headers: {
          'Authorization': `Token ${this.userToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          session_id: this.currentSessionId,
          action: 'resume'
        })
      });
    } catch (error) {
      console.error('Error resuming session:', error);
    }
  }

  async stopTracking() {
    if (!this.currentSessionId) return;
    
    // Stop heartbeat
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = null;
    }
    
    try {
      await fetch('/users/time-tracking/end-session/', {
        method: 'POST',
        headers: {
          'Authorization': `Token ${this.userToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ session_id: this.currentSessionId })
      });
      
      this.currentSessionId = null;
    } catch (error) {
      console.error('Error ending session:', error);
    }
  }
}

// Usage
const tracker = new DocumentTimeTracker(userToken);

// When user accesses document
tracker.startTracking(documentId);

// When user starts quiz
tracker.pauseForQuiz();

// When user finishes quiz
tracker.resumeAfterQuiz();

// When user leaves document
tracker.stopTracking();
```

## Data Models

### DocumentTimeSession
- `id`: Session ID
- `student`: User ID
- `document`: Document ID
- `session_start`: Start time (Indian timezone)
- `session_end`: End time (Indian timezone)
- `total_time_seconds`: Total time spent
- `status`: 'active', 'paused', 'completed', 'abandoned'
- `last_activity`: Last heartbeat timestamp

### DocumentTimeStats
- `total_study_time_seconds`: Total active study time
- `total_sessions`: Number of sessions
- `total_quiz_pauses`: Number of quiz pauses
- `view_count`: Number of times document was accessed
- `reopened_at_least_once`: True if view_count > 1
- `first_access`: First access timestamp
- `last_access`: Last access timestamp

## Notes

1. All timestamps are stored in Indian timezone (Asia/Kolkata)
2. Sessions are automatically marked as abandoned after 30 minutes of inactivity
3. Use the cleanup management command: `python manage.py cleanup_time_sessions`
4. The heartbeat mechanism helps detect when users leave without properly ending sessions
5. Quiz pauses are tracked separately from study time for accurate analytics
