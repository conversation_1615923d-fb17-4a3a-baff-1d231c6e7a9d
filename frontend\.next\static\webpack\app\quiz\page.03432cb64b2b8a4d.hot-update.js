"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/quiz/page",{

/***/ "(app-pages-browser)/./hooks/use-document-time.ts":
/*!************************************!*\
  !*** ./hooks/use-document-time.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDocumentTime: () => (/* binding */ useDocumentTime)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_navigation_events__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/navigation-events */ \"(app-pages-browser)/./lib/navigation-events.ts\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ useDocumentTime auto */ \n\n\nfunction useDocumentTime() {\n    let { documentId, enabled = true, isProcessingComplete = false, onQuizStart, onQuizEnd } = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    const sessionIdRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const isActiveRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const isPausedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const currentDocumentRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(documentId);\n    const sessionStartTimeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    // Get the correct API base URL\n    const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';\n    const getAuthHeaders = ()=>{\n        const token = localStorage.getItem('token');\n        return {\n            'Content-Type': 'application/json',\n            'Authorization': \"Token \".concat(token)\n        };\n    };\n    const startSession = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDocumentTime.useCallback[startSession]\": async ()=>{\n            if (!enabled || !documentId || !isProcessingComplete || isActiveRef.current) return;\n            try {\n                const token = localStorage.getItem('token');\n                if (!token) return;\n                const response = await fetch(\"\".concat(API_BASE_URL, \"/users/platform-time/start/\"), {\n                    method: 'POST',\n                    headers: getAuthHeaders(),\n                    body: JSON.stringify({\n                        document_id: documentId\n                    })\n                });\n                if (response.ok) {\n                    const data = await response.json();\n                    sessionIdRef.current = data.session_id;\n                    isActiveRef.current = true;\n                    isPausedRef.current = false;\n                    currentDocumentRef.current = documentId;\n                    sessionStartTimeRef.current = new Date() // Record when session started\n                    ;\n                    console.log('Document time session started:', data.session_id, data.message);\n                }\n            } catch (error) {\n                console.error('Error starting document time session:', error);\n            }\n        }\n    }[\"useDocumentTime.useCallback[startSession]\"], [\n        documentId,\n        enabled,\n        isProcessingComplete\n    ]);\n    const pauseSession = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDocumentTime.useCallback[pauseSession]\": async function() {\n            let reason = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'manual';\n            if (!documentId || !isActiveRef.current || isPausedRef.current) return;\n            try {\n                const token = localStorage.getItem('token');\n                if (!token) return;\n                const response = await fetch(\"\".concat(API_BASE_URL, \"/users/platform-time/pause/\"), {\n                    method: 'POST',\n                    headers: getAuthHeaders(),\n                    body: JSON.stringify({\n                        document_id: documentId,\n                        reason: reason\n                    })\n                });\n                if (response.ok) {\n                    isPausedRef.current = true;\n                    console.log(\"Document time session paused: \".concat(reason));\n                }\n            } catch (error) {\n                console.error('Error pausing document time session:', error);\n            }\n        }\n    }[\"useDocumentTime.useCallback[pauseSession]\"], [\n        documentId\n    ]);\n    const resumeSession = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDocumentTime.useCallback[resumeSession]\": async ()=>{\n            if (!documentId || !isActiveRef.current || !isPausedRef.current) return;\n            try {\n                const token = localStorage.getItem('token');\n                if (!token) return;\n                const response = await fetch(\"\".concat(API_BASE_URL, \"/users/platform-time/resume/\"), {\n                    method: 'POST',\n                    headers: getAuthHeaders(),\n                    body: JSON.stringify({\n                        document_id: documentId\n                    })\n                });\n                if (response.ok) {\n                    isPausedRef.current = false;\n                    console.log('Document time session resumed');\n                }\n            } catch (error) {\n                console.error('Error resuming document time session:', error);\n            }\n        }\n    }[\"useDocumentTime.useCallback[resumeSession]\"], [\n        documentId\n    ]);\n    const endSession = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDocumentTime.useCallback[endSession]\": async ()=>{\n            if (!documentId || !isActiveRef.current) return;\n            try {\n                const token = localStorage.getItem('token');\n                if (!token) return;\n                await fetch(\"\".concat(API_BASE_URL, \"/users/platform-time/end/\"), {\n                    method: 'POST',\n                    headers: getAuthHeaders(),\n                    body: JSON.stringify({\n                        document_id: documentId\n                    })\n                });\n                sessionIdRef.current = null;\n                isActiveRef.current = false;\n                isPausedRef.current = false;\n                sessionStartTimeRef.current = null;\n                console.log('Document time session ended');\n            } catch (error) {\n                console.error('Error ending document time session:', error);\n            }\n        }\n    }[\"useDocumentTime.useCallback[endSession]\"], [\n        documentId\n    ]);\n    // Quiz control functions\n    const startQuiz = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDocumentTime.useCallback[startQuiz]\": ()=>{\n            pauseSession('quiz');\n            onQuizStart === null || onQuizStart === void 0 ? void 0 : onQuizStart();\n        }\n    }[\"useDocumentTime.useCallback[startQuiz]\"], [\n        pauseSession,\n        onQuizStart\n    ]);\n    const endQuiz = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDocumentTime.useCallback[endQuiz]\": ()=>{\n            resumeSession();\n            onQuizEnd === null || onQuizEnd === void 0 ? void 0 : onQuizEnd();\n        }\n    }[\"useDocumentTime.useCallback[endQuiz]\"], [\n        resumeSession,\n        onQuizEnd\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useDocumentTime.useEffect\": ()=>{\n            if (!enabled || !documentId || !isProcessingComplete) return;\n            // If document changed, end previous session and start new one\n            if (currentDocumentRef.current !== documentId) {\n                if (isActiveRef.current) {\n                    endSession();\n                }\n                currentDocumentRef.current = documentId;\n            }\n            // Start session only when processing is complete (learning phase begins)\n            startSession();\n            // Handle page unload - end session when user leaves\n            const handleBeforeUnload = {\n                \"useDocumentTime.useEffect.handleBeforeUnload\": ()=>{\n                    endSession();\n                }\n            }[\"useDocumentTime.useEffect.handleBeforeUnload\"];\n            window.addEventListener('beforeunload', handleBeforeUnload);\n            return ({\n                \"useDocumentTime.useEffect\": ()=>{\n                    window.removeEventListener('beforeunload', handleBeforeUnload);\n                }\n            })[\"useDocumentTime.useEffect\"];\n        }\n    }[\"useDocumentTime.useEffect\"], [\n        documentId,\n        enabled,\n        isProcessingComplete,\n        startSession,\n        endSession\n    ]);\n    // Add cleanup effect for component unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useDocumentTime.useEffect\": ()=>{\n            return ({\n                \"useDocumentTime.useEffect\": ()=>{\n                    // End session when component unmounts (user navigates away)\n                    if (isActiveRef.current) {\n                        endSession();\n                    }\n                }\n            })[\"useDocumentTime.useEffect\"];\n        }\n    }[\"useDocumentTime.useEffect\"], [\n        endSession\n    ]);\n    // Listen for navigation events to end session\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useDocumentTime.useEffect\": ()=>{\n            if (!enabled || !documentId) return;\n            const handleNavigation = {\n                \"useDocumentTime.useEffect.handleNavigation\": ()=>{\n                    if (isActiveRef.current) {\n                        console.log('Navigation event detected, ending session for document:', documentId);\n                        endSession();\n                    }\n                }\n            }[\"useDocumentTime.useEffect.handleNavigation\"];\n            const removeListener = _lib_navigation_events__WEBPACK_IMPORTED_MODULE_2__.navigationEvents.addListener(handleNavigation);\n            return removeListener;\n        }\n    }[\"useDocumentTime.useEffect\"], [\n        enabled,\n        documentId,\n        endSession\n    ]);\n    return {\n        sessionId: sessionIdRef.current,\n        isActive: isActiveRef.current,\n        isPaused: isPausedRef.current,\n        sessionStartTime: sessionStartTimeRef.current,\n        startQuiz,\n        endQuiz,\n        pauseSession,\n        resumeSession,\n        endSession\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2hvb2tzL3VzZS1kb2N1bWVudC10aW1lLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztxRUFFc0Q7QUFDWDtBQUNlO0FBVW5ELFNBQVNLO1FBQWdCLEVBQzlCQyxVQUFVLEVBQ1ZDLFVBQVUsSUFBSSxFQUNkQyx1QkFBdUIsS0FBSyxFQUM1QkMsV0FBVyxFQUNYQyxTQUFTLEVBQ1ksR0FOUyxpRUFNTixDQUFDO0lBQ3pCLE1BQU1DLGVBQWVWLDZDQUFNQSxDQUFnQjtJQUMzQyxNQUFNVyxjQUFjWCw2Q0FBTUEsQ0FBQztJQUMzQixNQUFNWSxjQUFjWiw2Q0FBTUEsQ0FBQztJQUMzQixNQUFNYSxxQkFBcUJiLDZDQUFNQSxDQUFxQks7SUFDdEQsTUFBTVMsc0JBQXNCZCw2Q0FBTUEsQ0FBYztJQUNoRCxNQUFNZSxTQUFTYiwwREFBU0E7SUFFeEIsK0JBQStCO0lBQy9CLE1BQU1jLGVBQWVDLE9BQU9BLENBQUNDLEdBQUcsQ0FBQ0MsbUJBQW1CLElBQUk7SUFFeEQsTUFBTUMsaUJBQWlCO1FBQ3JCLE1BQU1DLFFBQVFDLGFBQWFDLE9BQU8sQ0FBQztRQUNuQyxPQUFPO1lBQ0wsZ0JBQWdCO1lBQ2hCLGlCQUFpQixTQUFlLE9BQU5GO1FBQzVCO0lBQ0Y7SUFFQSxNQUFNRyxlQUFldkIsa0RBQVdBO3FEQUFDO1lBQy9CLElBQUksQ0FBQ0ssV0FBVyxDQUFDRCxjQUFjLENBQUNFLHdCQUF3QkksWUFBWWMsT0FBTyxFQUFFO1lBRTdFLElBQUk7Z0JBQ0YsTUFBTUosUUFBUUMsYUFBYUMsT0FBTyxDQUFDO2dCQUNuQyxJQUFJLENBQUNGLE9BQU87Z0JBRVosTUFBTUssV0FBVyxNQUFNQyxNQUFNLEdBQWdCLE9BQWJYLGNBQWEsZ0NBQThCO29CQUN6RVksUUFBUTtvQkFDUkMsU0FBU1Q7b0JBQ1RVLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQzt3QkFBRUMsYUFBYTVCO29CQUFXO2dCQUNqRDtnQkFFQSxJQUFJcUIsU0FBU1EsRUFBRSxFQUFFO29CQUNmLE1BQU1DLE9BQU8sTUFBTVQsU0FBU1UsSUFBSTtvQkFDaEMxQixhQUFhZSxPQUFPLEdBQUdVLEtBQUtFLFVBQVU7b0JBQ3RDMUIsWUFBWWMsT0FBTyxHQUFHO29CQUN0QmIsWUFBWWEsT0FBTyxHQUFHO29CQUN0QlosbUJBQW1CWSxPQUFPLEdBQUdwQjtvQkFDN0JTLG9CQUFvQlcsT0FBTyxHQUFHLElBQUlhLE9BQU8sOEJBQThCOztvQkFDdkVDLFFBQVFDLEdBQUcsQ0FBQyxrQ0FBa0NMLEtBQUtFLFVBQVUsRUFBRUYsS0FBS00sT0FBTztnQkFDN0U7WUFDRixFQUFFLE9BQU9DLE9BQU87Z0JBQ2RILFFBQVFHLEtBQUssQ0FBQyx5Q0FBeUNBO1lBQ3pEO1FBQ0Y7b0RBQUc7UUFBQ3JDO1FBQVlDO1FBQVNDO0tBQXFCO0lBRTlDLE1BQU1vQyxlQUFlMUMsa0RBQVdBO3FEQUFDO2dCQUFPMkMsMEVBQTJDO1lBQ2pGLElBQUksQ0FBQ3ZDLGNBQWMsQ0FBQ00sWUFBWWMsT0FBTyxJQUFJYixZQUFZYSxPQUFPLEVBQUU7WUFFaEUsSUFBSTtnQkFDRixNQUFNSixRQUFRQyxhQUFhQyxPQUFPLENBQUM7Z0JBQ25DLElBQUksQ0FBQ0YsT0FBTztnQkFFWixNQUFNSyxXQUFXLE1BQU1DLE1BQU0sR0FBZ0IsT0FBYlgsY0FBYSxnQ0FBOEI7b0JBQ3pFWSxRQUFRO29CQUNSQyxTQUFTVDtvQkFDVFUsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO3dCQUNuQkMsYUFBYTVCO3dCQUNidUMsUUFBUUE7b0JBQ1Y7Z0JBQ0Y7Z0JBRUEsSUFBSWxCLFNBQVNRLEVBQUUsRUFBRTtvQkFDZnRCLFlBQVlhLE9BQU8sR0FBRztvQkFDdEJjLFFBQVFDLEdBQUcsQ0FBQyxpQ0FBd0MsT0FBUEk7Z0JBQy9DO1lBQ0YsRUFBRSxPQUFPRixPQUFPO2dCQUNkSCxRQUFRRyxLQUFLLENBQUMsd0NBQXdDQTtZQUN4RDtRQUNGO29EQUFHO1FBQUNyQztLQUFXO0lBRWYsTUFBTXdDLGdCQUFnQjVDLGtEQUFXQTtzREFBQztZQUNoQyxJQUFJLENBQUNJLGNBQWMsQ0FBQ00sWUFBWWMsT0FBTyxJQUFJLENBQUNiLFlBQVlhLE9BQU8sRUFBRTtZQUVqRSxJQUFJO2dCQUNGLE1BQU1KLFFBQVFDLGFBQWFDLE9BQU8sQ0FBQztnQkFDbkMsSUFBSSxDQUFDRixPQUFPO2dCQUVaLE1BQU1LLFdBQVcsTUFBTUMsTUFBTSxHQUFnQixPQUFiWCxjQUFhLGlDQUErQjtvQkFDMUVZLFFBQVE7b0JBQ1JDLFNBQVNUO29CQUNUVSxNQUFNQyxLQUFLQyxTQUFTLENBQUM7d0JBQUVDLGFBQWE1QjtvQkFBVztnQkFDakQ7Z0JBRUEsSUFBSXFCLFNBQVNRLEVBQUUsRUFBRTtvQkFDZnRCLFlBQVlhLE9BQU8sR0FBRztvQkFDdEJjLFFBQVFDLEdBQUcsQ0FBQztnQkFDZDtZQUNGLEVBQUUsT0FBT0UsT0FBTztnQkFDZEgsUUFBUUcsS0FBSyxDQUFDLHlDQUF5Q0E7WUFDekQ7UUFDRjtxREFBRztRQUFDckM7S0FBVztJQUVmLE1BQU15QyxhQUFhN0Msa0RBQVdBO21EQUFDO1lBQzdCLElBQUksQ0FBQ0ksY0FBYyxDQUFDTSxZQUFZYyxPQUFPLEVBQUU7WUFFekMsSUFBSTtnQkFDRixNQUFNSixRQUFRQyxhQUFhQyxPQUFPLENBQUM7Z0JBQ25DLElBQUksQ0FBQ0YsT0FBTztnQkFFWixNQUFNTSxNQUFNLEdBQWdCLE9BQWJYLGNBQWEsOEJBQTRCO29CQUN0RFksUUFBUTtvQkFDUkMsU0FBU1Q7b0JBQ1RVLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQzt3QkFBRUMsYUFBYTVCO29CQUFXO2dCQUNqRDtnQkFFQUssYUFBYWUsT0FBTyxHQUFHO2dCQUN2QmQsWUFBWWMsT0FBTyxHQUFHO2dCQUN0QmIsWUFBWWEsT0FBTyxHQUFHO2dCQUN0Qlgsb0JBQW9CVyxPQUFPLEdBQUc7Z0JBQzlCYyxRQUFRQyxHQUFHLENBQUM7WUFDZCxFQUFFLE9BQU9FLE9BQU87Z0JBQ2RILFFBQVFHLEtBQUssQ0FBQyx1Q0FBdUNBO1lBQ3ZEO1FBQ0Y7a0RBQUc7UUFBQ3JDO0tBQVc7SUFFZix5QkFBeUI7SUFDekIsTUFBTTBDLFlBQVk5QyxrREFBV0E7a0RBQUM7WUFDNUIwQyxhQUFhO1lBQ2JuQyx3QkFBQUEsa0NBQUFBO1FBQ0Y7aURBQUc7UUFBQ21DO1FBQWNuQztLQUFZO0lBRTlCLE1BQU13QyxVQUFVL0Msa0RBQVdBO2dEQUFDO1lBQzFCNEM7WUFDQXBDLHNCQUFBQSxnQ0FBQUE7UUFDRjsrQ0FBRztRQUFDb0M7UUFBZXBDO0tBQVU7SUFFN0JWLGdEQUFTQTtxQ0FBQztZQUNSLElBQUksQ0FBQ08sV0FBVyxDQUFDRCxjQUFjLENBQUNFLHNCQUFzQjtZQUV0RCw4REFBOEQ7WUFDOUQsSUFBSU0sbUJBQW1CWSxPQUFPLEtBQUtwQixZQUFZO2dCQUM3QyxJQUFJTSxZQUFZYyxPQUFPLEVBQUU7b0JBQ3ZCcUI7Z0JBQ0Y7Z0JBQ0FqQyxtQkFBbUJZLE9BQU8sR0FBR3BCO1lBQy9CO1lBRUEseUVBQXlFO1lBQ3pFbUI7WUFFQSxvREFBb0Q7WUFDcEQsTUFBTXlCO2dFQUFxQjtvQkFDekJIO2dCQUNGOztZQUVBSSxPQUFPQyxnQkFBZ0IsQ0FBQyxnQkFBZ0JGO1lBRXhDOzZDQUFPO29CQUNMQyxPQUFPRSxtQkFBbUIsQ0FBQyxnQkFBZ0JIO2dCQUM3Qzs7UUFDRjtvQ0FBRztRQUFDNUM7UUFBWUM7UUFBU0M7UUFBc0JpQjtRQUFjc0I7S0FBVztJQUV4RSwyQ0FBMkM7SUFDM0MvQyxnREFBU0E7cUNBQUM7WUFDUjs2Q0FBTztvQkFDTCw0REFBNEQ7b0JBQzVELElBQUlZLFlBQVljLE9BQU8sRUFBRTt3QkFDdkJxQjtvQkFDRjtnQkFDRjs7UUFDRjtvQ0FBRztRQUFDQTtLQUFXO0lBRWYsOENBQThDO0lBQzlDL0MsZ0RBQVNBO3FDQUFDO1lBQ1IsSUFBSSxDQUFDTyxXQUFXLENBQUNELFlBQVk7WUFFN0IsTUFBTWdEOzhEQUFtQjtvQkFDdkIsSUFBSTFDLFlBQVljLE9BQU8sRUFBRTt3QkFDdkJjLFFBQVFDLEdBQUcsQ0FBQywyREFBMkRuQzt3QkFDdkV5QztvQkFDRjtnQkFDRjs7WUFFQSxNQUFNUSxpQkFBaUJuRCxvRUFBZ0JBLENBQUNvRCxXQUFXLENBQUNGO1lBQ3BELE9BQU9DO1FBQ1Q7b0NBQUc7UUFBQ2hEO1FBQVNEO1FBQVl5QztLQUFXO0lBRXBDLE9BQU87UUFDTFUsV0FBVzlDLGFBQWFlLE9BQU87UUFDL0JnQyxVQUFVOUMsWUFBWWMsT0FBTztRQUM3QmlDLFVBQVU5QyxZQUFZYSxPQUFPO1FBQzdCa0Msa0JBQWtCN0Msb0JBQW9CVyxPQUFPO1FBQzdDc0I7UUFDQUM7UUFDQUw7UUFDQUU7UUFDQUM7SUFDRjtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGpvdmFuXFxPbmVEcml2ZVxcRGVza3RvcFxcY29nbmltb3NpdHlfVUlcXGxhdGVzdCByZXBvXFxjb2duaV9hcGlcXGZyb250ZW5kXFxob29rc1xcdXNlLWRvY3VtZW50LXRpbWUudHMiXSwic291cmNlc0NvbnRlbnQiOlsiJ3VzZSBjbGllbnQnXG5cbmltcG9ydCB7IHVzZUVmZmVjdCwgdXNlUmVmLCB1c2VDYWxsYmFjayB9IGZyb20gJ3JlYWN0J1xuaW1wb3J0IHsgdXNlUm91dGVyIH0gZnJvbSAnbmV4dC9uYXZpZ2F0aW9uJ1xuaW1wb3J0IHsgbmF2aWdhdGlvbkV2ZW50cyB9IGZyb20gJ0AvbGliL25hdmlnYXRpb24tZXZlbnRzJ1xuXG5pbnRlcmZhY2UgVXNlRG9jdW1lbnRUaW1lUHJvcHMge1xuICBkb2N1bWVudElkPzogbnVtYmVyXG4gIGVuYWJsZWQ/OiBib29sZWFuXG4gIGlzUHJvY2Vzc2luZ0NvbXBsZXRlPzogYm9vbGVhbiAgLy8gTmV3IHByb3AgdG8gY29udHJvbCB3aGVuIHRyYWNraW5nIHN0YXJ0c1xuICBvblF1aXpTdGFydD86ICgpID0+IHZvaWRcbiAgb25RdWl6RW5kPzogKCkgPT4gdm9pZFxufVxuXG5leHBvcnQgZnVuY3Rpb24gdXNlRG9jdW1lbnRUaW1lKHtcbiAgZG9jdW1lbnRJZCxcbiAgZW5hYmxlZCA9IHRydWUsXG4gIGlzUHJvY2Vzc2luZ0NvbXBsZXRlID0gZmFsc2UsICAvLyBPbmx5IHN0YXJ0IHRyYWNraW5nIGFmdGVyIHByb2Nlc3NpbmcgaXMgY29tcGxldGVcbiAgb25RdWl6U3RhcnQsXG4gIG9uUXVpekVuZFxufTogVXNlRG9jdW1lbnRUaW1lUHJvcHMgPSB7fSkge1xuICBjb25zdCBzZXNzaW9uSWRSZWYgPSB1c2VSZWY8bnVtYmVyIHwgbnVsbD4obnVsbClcbiAgY29uc3QgaXNBY3RpdmVSZWYgPSB1c2VSZWYoZmFsc2UpXG4gIGNvbnN0IGlzUGF1c2VkUmVmID0gdXNlUmVmKGZhbHNlKVxuICBjb25zdCBjdXJyZW50RG9jdW1lbnRSZWYgPSB1c2VSZWY8bnVtYmVyIHwgdW5kZWZpbmVkPihkb2N1bWVudElkKVxuICBjb25zdCBzZXNzaW9uU3RhcnRUaW1lUmVmID0gdXNlUmVmPERhdGUgfCBudWxsPihudWxsKVxuICBjb25zdCByb3V0ZXIgPSB1c2VSb3V0ZXIoKVxuXG4gIC8vIEdldCB0aGUgY29ycmVjdCBBUEkgYmFzZSBVUkxcbiAgY29uc3QgQVBJX0JBU0VfVVJMID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVBJX1VSTCB8fCAnaHR0cDovL2xvY2FsaG9zdDo4MDAwL2FwaSdcblxuICBjb25zdCBnZXRBdXRoSGVhZGVycyA9ICgpID0+IHtcbiAgICBjb25zdCB0b2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd0b2tlbicpXG4gICAgcmV0dXJuIHtcbiAgICAgICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicsXG4gICAgICAnQXV0aG9yaXphdGlvbic6IGBUb2tlbiAke3Rva2VufWAsXG4gICAgfVxuICB9XG5cbiAgY29uc3Qgc3RhcnRTZXNzaW9uID0gdXNlQ2FsbGJhY2soYXN5bmMgKCkgPT4ge1xuICAgIGlmICghZW5hYmxlZCB8fCAhZG9jdW1lbnRJZCB8fCAhaXNQcm9jZXNzaW5nQ29tcGxldGUgfHwgaXNBY3RpdmVSZWYuY3VycmVudCkgcmV0dXJuXG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgdG9rZW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndG9rZW4nKVxuICAgICAgaWYgKCF0b2tlbikgcmV0dXJuXG5cbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7QVBJX0JBU0VfVVJMfS91c2Vycy9wbGF0Zm9ybS10aW1lL3N0YXJ0L2AsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnM6IGdldEF1dGhIZWFkZXJzKCksXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHsgZG9jdW1lbnRfaWQ6IGRvY3VtZW50SWQgfSksXG4gICAgICB9KVxuXG4gICAgICBpZiAocmVzcG9uc2Uub2spIHtcbiAgICAgICAgY29uc3QgZGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKVxuICAgICAgICBzZXNzaW9uSWRSZWYuY3VycmVudCA9IGRhdGEuc2Vzc2lvbl9pZFxuICAgICAgICBpc0FjdGl2ZVJlZi5jdXJyZW50ID0gdHJ1ZVxuICAgICAgICBpc1BhdXNlZFJlZi5jdXJyZW50ID0gZmFsc2VcbiAgICAgICAgY3VycmVudERvY3VtZW50UmVmLmN1cnJlbnQgPSBkb2N1bWVudElkXG4gICAgICAgIHNlc3Npb25TdGFydFRpbWVSZWYuY3VycmVudCA9IG5ldyBEYXRlKCkgLy8gUmVjb3JkIHdoZW4gc2Vzc2lvbiBzdGFydGVkXG4gICAgICAgIGNvbnNvbGUubG9nKCdEb2N1bWVudCB0aW1lIHNlc3Npb24gc3RhcnRlZDonLCBkYXRhLnNlc3Npb25faWQsIGRhdGEubWVzc2FnZSlcbiAgICAgIH1cbiAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgY29uc29sZS5lcnJvcignRXJyb3Igc3RhcnRpbmcgZG9jdW1lbnQgdGltZSBzZXNzaW9uOicsIGVycm9yKVxuICAgIH1cbiAgfSwgW2RvY3VtZW50SWQsIGVuYWJsZWQsIGlzUHJvY2Vzc2luZ0NvbXBsZXRlXSlcblxuICBjb25zdCBwYXVzZVNlc3Npb24gPSB1c2VDYWxsYmFjayhhc3luYyAocmVhc29uOiAncXVpeicgfCAnbmF2aWdhdGlvbicgfCAnbWFudWFsJyA9ICdtYW51YWwnKSA9PiB7XG4gICAgaWYgKCFkb2N1bWVudElkIHx8ICFpc0FjdGl2ZVJlZi5jdXJyZW50IHx8IGlzUGF1c2VkUmVmLmN1cnJlbnQpIHJldHVyblxuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3Rva2VuJylcbiAgICAgIGlmICghdG9rZW4pIHJldHVyblxuXG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKGAke0FQSV9CQVNFX1VSTH0vdXNlcnMvcGxhdGZvcm0tdGltZS9wYXVzZS9gLCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBoZWFkZXJzOiBnZXRBdXRoSGVhZGVycygpLFxuICAgICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgICAgZG9jdW1lbnRfaWQ6IGRvY3VtZW50SWQsXG4gICAgICAgICAgcmVhc29uOiByZWFzb25cbiAgICAgICAgfSksXG4gICAgICB9KVxuXG4gICAgICBpZiAocmVzcG9uc2Uub2spIHtcbiAgICAgICAgaXNQYXVzZWRSZWYuY3VycmVudCA9IHRydWVcbiAgICAgICAgY29uc29sZS5sb2coYERvY3VtZW50IHRpbWUgc2Vzc2lvbiBwYXVzZWQ6ICR7cmVhc29ufWApXG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHBhdXNpbmcgZG9jdW1lbnQgdGltZSBzZXNzaW9uOicsIGVycm9yKVxuICAgIH1cbiAgfSwgW2RvY3VtZW50SWRdKVxuXG4gIGNvbnN0IHJlc3VtZVNlc3Npb24gPSB1c2VDYWxsYmFjayhhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFkb2N1bWVudElkIHx8ICFpc0FjdGl2ZVJlZi5jdXJyZW50IHx8ICFpc1BhdXNlZFJlZi5jdXJyZW50KSByZXR1cm5cblxuICAgIHRyeSB7XG4gICAgICBjb25zdCB0b2tlbiA9IGxvY2FsU3RvcmFnZS5nZXRJdGVtKCd0b2tlbicpXG4gICAgICBpZiAoIXRva2VuKSByZXR1cm5cblxuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaChgJHtBUElfQkFTRV9VUkx9L3VzZXJzL3BsYXRmb3JtLXRpbWUvcmVzdW1lL2AsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnM6IGdldEF1dGhIZWFkZXJzKCksXG4gICAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHsgZG9jdW1lbnRfaWQ6IGRvY3VtZW50SWQgfSksXG4gICAgICB9KVxuXG4gICAgICBpZiAocmVzcG9uc2Uub2spIHtcbiAgICAgICAgaXNQYXVzZWRSZWYuY3VycmVudCA9IGZhbHNlXG4gICAgICAgIGNvbnNvbGUubG9nKCdEb2N1bWVudCB0aW1lIHNlc3Npb24gcmVzdW1lZCcpXG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIHJlc3VtaW5nIGRvY3VtZW50IHRpbWUgc2Vzc2lvbjonLCBlcnJvcilcbiAgICB9XG4gIH0sIFtkb2N1bWVudElkXSlcblxuICBjb25zdCBlbmRTZXNzaW9uID0gdXNlQ2FsbGJhY2soYXN5bmMgKCkgPT4ge1xuICAgIGlmICghZG9jdW1lbnRJZCB8fCAhaXNBY3RpdmVSZWYuY3VycmVudCkgcmV0dXJuXG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgdG9rZW4gPSBsb2NhbFN0b3JhZ2UuZ2V0SXRlbSgndG9rZW4nKVxuICAgICAgaWYgKCF0b2tlbikgcmV0dXJuXG5cbiAgICAgIGF3YWl0IGZldGNoKGAke0FQSV9CQVNFX1VSTH0vdXNlcnMvcGxhdGZvcm0tdGltZS9lbmQvYCwge1xuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgaGVhZGVyczogZ2V0QXV0aEhlYWRlcnMoKSxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoeyBkb2N1bWVudF9pZDogZG9jdW1lbnRJZCB9KSxcbiAgICAgIH0pXG5cbiAgICAgIHNlc3Npb25JZFJlZi5jdXJyZW50ID0gbnVsbFxuICAgICAgaXNBY3RpdmVSZWYuY3VycmVudCA9IGZhbHNlXG4gICAgICBpc1BhdXNlZFJlZi5jdXJyZW50ID0gZmFsc2VcbiAgICAgIHNlc3Npb25TdGFydFRpbWVSZWYuY3VycmVudCA9IG51bGxcbiAgICAgIGNvbnNvbGUubG9nKCdEb2N1bWVudCB0aW1lIHNlc3Npb24gZW5kZWQnKVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBlbmRpbmcgZG9jdW1lbnQgdGltZSBzZXNzaW9uOicsIGVycm9yKVxuICAgIH1cbiAgfSwgW2RvY3VtZW50SWRdKVxuXG4gIC8vIFF1aXogY29udHJvbCBmdW5jdGlvbnNcbiAgY29uc3Qgc3RhcnRRdWl6ID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIHBhdXNlU2Vzc2lvbigncXVpeicpXG4gICAgb25RdWl6U3RhcnQ/LigpXG4gIH0sIFtwYXVzZVNlc3Npb24sIG9uUXVpelN0YXJ0XSlcblxuICBjb25zdCBlbmRRdWl6ID0gdXNlQ2FsbGJhY2soKCkgPT4ge1xuICAgIHJlc3VtZVNlc3Npb24oKVxuICAgIG9uUXVpekVuZD8uKClcbiAgfSwgW3Jlc3VtZVNlc3Npb24sIG9uUXVpekVuZF0pXG5cbiAgdXNlRWZmZWN0KCgpID0+IHtcbiAgICBpZiAoIWVuYWJsZWQgfHwgIWRvY3VtZW50SWQgfHwgIWlzUHJvY2Vzc2luZ0NvbXBsZXRlKSByZXR1cm5cblxuICAgIC8vIElmIGRvY3VtZW50IGNoYW5nZWQsIGVuZCBwcmV2aW91cyBzZXNzaW9uIGFuZCBzdGFydCBuZXcgb25lXG4gICAgaWYgKGN1cnJlbnREb2N1bWVudFJlZi5jdXJyZW50ICE9PSBkb2N1bWVudElkKSB7XG4gICAgICBpZiAoaXNBY3RpdmVSZWYuY3VycmVudCkge1xuICAgICAgICBlbmRTZXNzaW9uKClcbiAgICAgIH1cbiAgICAgIGN1cnJlbnREb2N1bWVudFJlZi5jdXJyZW50ID0gZG9jdW1lbnRJZFxuICAgIH1cblxuICAgIC8vIFN0YXJ0IHNlc3Npb24gb25seSB3aGVuIHByb2Nlc3NpbmcgaXMgY29tcGxldGUgKGxlYXJuaW5nIHBoYXNlIGJlZ2lucylcbiAgICBzdGFydFNlc3Npb24oKVxuXG4gICAgLy8gSGFuZGxlIHBhZ2UgdW5sb2FkIC0gZW5kIHNlc3Npb24gd2hlbiB1c2VyIGxlYXZlc1xuICAgIGNvbnN0IGhhbmRsZUJlZm9yZVVubG9hZCA9ICgpID0+IHtcbiAgICAgIGVuZFNlc3Npb24oKVxuICAgIH1cblxuICAgIHdpbmRvdy5hZGRFdmVudExpc3RlbmVyKCdiZWZvcmV1bmxvYWQnLCBoYW5kbGVCZWZvcmVVbmxvYWQpXG5cbiAgICByZXR1cm4gKCkgPT4ge1xuICAgICAgd2luZG93LnJlbW92ZUV2ZW50TGlzdGVuZXIoJ2JlZm9yZXVubG9hZCcsIGhhbmRsZUJlZm9yZVVubG9hZClcbiAgICB9XG4gIH0sIFtkb2N1bWVudElkLCBlbmFibGVkLCBpc1Byb2Nlc3NpbmdDb21wbGV0ZSwgc3RhcnRTZXNzaW9uLCBlbmRTZXNzaW9uXSlcblxuICAvLyBBZGQgY2xlYW51cCBlZmZlY3QgZm9yIGNvbXBvbmVudCB1bm1vdW50XG4gIHVzZUVmZmVjdCgoKSA9PiB7XG4gICAgcmV0dXJuICgpID0+IHtcbiAgICAgIC8vIEVuZCBzZXNzaW9uIHdoZW4gY29tcG9uZW50IHVubW91bnRzICh1c2VyIG5hdmlnYXRlcyBhd2F5KVxuICAgICAgaWYgKGlzQWN0aXZlUmVmLmN1cnJlbnQpIHtcbiAgICAgICAgZW5kU2Vzc2lvbigpXG4gICAgICB9XG4gICAgfVxuICB9LCBbZW5kU2Vzc2lvbl0pXG5cbiAgLy8gTGlzdGVuIGZvciBuYXZpZ2F0aW9uIGV2ZW50cyB0byBlbmQgc2Vzc2lvblxuICB1c2VFZmZlY3QoKCkgPT4ge1xuICAgIGlmICghZW5hYmxlZCB8fCAhZG9jdW1lbnRJZCkgcmV0dXJuXG5cbiAgICBjb25zdCBoYW5kbGVOYXZpZ2F0aW9uID0gKCkgPT4ge1xuICAgICAgaWYgKGlzQWN0aXZlUmVmLmN1cnJlbnQpIHtcbiAgICAgICAgY29uc29sZS5sb2coJ05hdmlnYXRpb24gZXZlbnQgZGV0ZWN0ZWQsIGVuZGluZyBzZXNzaW9uIGZvciBkb2N1bWVudDonLCBkb2N1bWVudElkKVxuICAgICAgICBlbmRTZXNzaW9uKClcbiAgICAgIH1cbiAgICB9XG5cbiAgICBjb25zdCByZW1vdmVMaXN0ZW5lciA9IG5hdmlnYXRpb25FdmVudHMuYWRkTGlzdGVuZXIoaGFuZGxlTmF2aWdhdGlvbilcbiAgICByZXR1cm4gcmVtb3ZlTGlzdGVuZXJcbiAgfSwgW2VuYWJsZWQsIGRvY3VtZW50SWQsIGVuZFNlc3Npb25dKVxuXG4gIHJldHVybiB7XG4gICAgc2Vzc2lvbklkOiBzZXNzaW9uSWRSZWYuY3VycmVudCxcbiAgICBpc0FjdGl2ZTogaXNBY3RpdmVSZWYuY3VycmVudCxcbiAgICBpc1BhdXNlZDogaXNQYXVzZWRSZWYuY3VycmVudCxcbiAgICBzZXNzaW9uU3RhcnRUaW1lOiBzZXNzaW9uU3RhcnRUaW1lUmVmLmN1cnJlbnQsXG4gICAgc3RhcnRRdWl6LFxuICAgIGVuZFF1aXosXG4gICAgcGF1c2VTZXNzaW9uLFxuICAgIHJlc3VtZVNlc3Npb24sXG4gICAgZW5kU2Vzc2lvbixcbiAgfVxufVxuIl0sIm5hbWVzIjpbInVzZUVmZmVjdCIsInVzZVJlZiIsInVzZUNhbGxiYWNrIiwidXNlUm91dGVyIiwibmF2aWdhdGlvbkV2ZW50cyIsInVzZURvY3VtZW50VGltZSIsImRvY3VtZW50SWQiLCJlbmFibGVkIiwiaXNQcm9jZXNzaW5nQ29tcGxldGUiLCJvblF1aXpTdGFydCIsIm9uUXVpekVuZCIsInNlc3Npb25JZFJlZiIsImlzQWN0aXZlUmVmIiwiaXNQYXVzZWRSZWYiLCJjdXJyZW50RG9jdW1lbnRSZWYiLCJzZXNzaW9uU3RhcnRUaW1lUmVmIiwicm91dGVyIiwiQVBJX0JBU0VfVVJMIiwicHJvY2VzcyIsImVudiIsIk5FWFRfUFVCTElDX0FQSV9VUkwiLCJnZXRBdXRoSGVhZGVycyIsInRva2VuIiwibG9jYWxTdG9yYWdlIiwiZ2V0SXRlbSIsInN0YXJ0U2Vzc2lvbiIsImN1cnJlbnQiLCJyZXNwb25zZSIsImZldGNoIiwibWV0aG9kIiwiaGVhZGVycyIsImJvZHkiLCJKU09OIiwic3RyaW5naWZ5IiwiZG9jdW1lbnRfaWQiLCJvayIsImRhdGEiLCJqc29uIiwic2Vzc2lvbl9pZCIsIkRhdGUiLCJjb25zb2xlIiwibG9nIiwibWVzc2FnZSIsImVycm9yIiwicGF1c2VTZXNzaW9uIiwicmVhc29uIiwicmVzdW1lU2Vzc2lvbiIsImVuZFNlc3Npb24iLCJzdGFydFF1aXoiLCJlbmRRdWl6IiwiaGFuZGxlQmVmb3JlVW5sb2FkIiwid2luZG93IiwiYWRkRXZlbnRMaXN0ZW5lciIsInJlbW92ZUV2ZW50TGlzdGVuZXIiLCJoYW5kbGVOYXZpZ2F0aW9uIiwicmVtb3ZlTGlzdGVuZXIiLCJhZGRMaXN0ZW5lciIsInNlc3Npb25JZCIsImlzQWN0aXZlIiwiaXNQYXVzZWQiLCJzZXNzaW9uU3RhcnRUaW1lIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/use-document-time.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/navigation-events.ts":
/*!**********************************!*\
  !*** ./lib/navigation-events.ts ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createNavigationWrapper: () => (/* binding */ createNavigationWrapper),\n/* harmony export */   navigationEvents: () => (/* binding */ navigationEvents)\n/* harmony export */ });\n// Navigation event system for time tracking cleanup\nclass NavigationEventManager {\n    // Add a listener for navigation events\n    addListener(callback) {\n        this.listeners.add(callback);\n        return ()=>this.listeners.delete(callback);\n    }\n    // Trigger navigation event (call this before any navigation)\n    triggerNavigation() {\n        this.listeners.forEach((callback)=>{\n            try {\n                callback();\n            } catch (error) {\n                console.error('Navigation event listener error:', error);\n            }\n        });\n    }\n    // Clear all listeners\n    clear() {\n        this.listeners.clear();\n    }\n    constructor(){\n        this.listeners = new Set();\n    }\n}\nconst navigationEvents = new NavigationEventManager();\n// Helper function to wrap router navigation with event triggering\nconst createNavigationWrapper = (router)=>{\n    const originalPush = router.push;\n    const originalReplace = router.replace;\n    const originalBack = router.back;\n    return {\n        push: function() {\n            for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n                args[_key] = arguments[_key];\n            }\n            navigationEvents.triggerNavigation();\n            return originalPush.apply(router, args);\n        },\n        replace: function() {\n            for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n                args[_key] = arguments[_key];\n            }\n            navigationEvents.triggerNavigation();\n            return originalReplace.apply(router, args);\n        },\n        back: function() {\n            for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n                args[_key] = arguments[_key];\n            }\n            navigationEvents.triggerNavigation();\n            return originalBack.apply(router, args);\n        },\n        // Restore original methods\n        restore: ()=>{\n            router.push = originalPush;\n            router.replace = originalReplace;\n            router.back = originalBack;\n        }\n    };\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/navigation-events.ts\n"));

/***/ })

});