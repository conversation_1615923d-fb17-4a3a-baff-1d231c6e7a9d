from django.core.management.base import BaseCommand
from django.utils import timezone
from users.models import PlatformTimeSession, PlatformTimeEvent
import pytz


class Command(BaseCommand):
    help = 'Clean up orphaned and stuck timer sessions'

    def add_arguments(self, parser):
        parser.add_argument(
            '--max-age-hours',
            type=int,
            default=24,
            help='Maximum age in hours for paused sessions before cleanup (default: 24)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be cleaned up without actually doing it'
        )

    def handle(self, *args, **options):
        max_age_hours = options['max_age_hours']
        dry_run = options['dry_run']
        
        self.stdout.write(f"Looking for orphaned sessions older than {max_age_hours} hours...")
        
        cutoff_time = timezone.now() - timezone.timedelta(hours=max_age_hours)
        indian_tz = pytz.timezone('Asia/Kolkata')
        
        # Find orphaned paused sessions
        orphaned_sessions = PlatformTimeSession.objects.filter(
            is_active=True,
            is_paused=True,
            last_activity__lt=cutoff_time
        )
        
        # Find sessions that are active but very old (likely stuck)
        stuck_sessions = PlatformTimeSession.objects.filter(
            is_active=True,
            is_paused=False,
            last_activity__lt=timezone.now() - timezone.timedelta(hours=max_age_hours * 2)
        )
        
        total_count = orphaned_sessions.count() + stuck_sessions.count()
        
        if total_count == 0:
            self.stdout.write(self.style.SUCCESS("No orphaned sessions found."))
            return
            
        self.stdout.write(f"Found {orphaned_sessions.count()} orphaned paused sessions")
        self.stdout.write(f"Found {stuck_sessions.count()} stuck active sessions")
        
        if dry_run:
            self.stdout.write(self.style.WARNING("DRY RUN - No changes will be made"))
            for session in orphaned_sessions:
                self.stdout.write(f"Would end paused session: {session.id} (Document: {session.document.title}, User: {session.student.username})")
            for session in stuck_sessions:
                self.stdout.write(f"Would end stuck session: {session.id} (Document: {session.document.title}, User: {session.student.username})")
            return
            
        # Clean up orphaned sessions
        cleaned_count = 0
        for session in orphaned_sessions:
            session.end_session()
            PlatformTimeEvent.objects.create(
                session=session,
                event_type='end',
                reason='Cleanup orphaned session',
                timestamp=timezone.now().astimezone(indian_tz)
            )
            cleaned_count += 1
            self.stdout.write(f"Ended orphaned session: {session.id}")
            
        # Clean up stuck sessions
        for session in stuck_sessions:
            session.end_session()
            PlatformTimeEvent.objects.create(
                session=session,
                event_type='end',
                reason='Cleanup stuck session',
                timestamp=timezone.now().astimezone(indian_tz)
            )
            cleaned_count += 1
            self.stdout.write(f"Ended stuck session: {session.id}")
            
        self.stdout.write(
            self.style.SUCCESS(f"Successfully cleaned up {cleaned_count} sessions.")
        )
