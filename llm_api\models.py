from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
import numpy as np

class UserInfo(BaseModel):
    """User information for authentication and tracking"""
    id: int
    username: str
    email: Optional[str] = None
    token: Optional[str] = None  # Store the authentication token for use in subsequent requests

class InferenceRequest(BaseModel):
    """Request model for LLM inference"""
    model: str
    message: str
    context: Optional[str] = None

    class Config:
        schema_extra = {
            "example": {
                "model": "gemini",
                "message": "What is the capital of France?",
                "context": "You are a helpful assistant."
            }
        }

class InferenceResponse(BaseModel):
    """Response model for LLM inference"""
    response: str
    model: str
    tokens: int
    usage_stats: Optional[Dict[str, Any]] = None

    class Config:
        schema_extra = {
            "example": {
                "response": "The capital of France is Paris.",
                "model": "gemini",
                "tokens": 10,
                "usage_stats": None
            }
        }

class DocumentProcessRequest(BaseModel):
    """Request model for document processing"""
    document_id: int
    user_id: int

class DocumentProcessResponse(BaseModel):
    """Response model for document processing"""
    message: str
    document_id: int
    num_chunks: int
    status: str

class BlueprintProcessRequest(BaseModel):
    """Request model for blueprint processing"""
    document_id: int
    user_id: int
    blueprint_text: str
    llm_model: str = "gemini"  # Default to gemini, but can be changed to "gemini"

class BlueprintTopic(BaseModel):
    """Model for a blueprint topic"""
    title: str
    weightage: float

class BlueprintProcessResponse(BaseModel):
    """Response model for blueprint processing"""
    message: str
    document_id: int
    topics: List[BlueprintTopic]
    status: str

class TextChunk(BaseModel):
    """Model for a text chunk with its embedding"""
    text: str
    embedding: List[float]
    chunk_number: int

class DocumentEmbedding(BaseModel):
    """Model for document embeddings"""
    document_id: int
    chunks: List[TextChunk]

class QuizQuestion(BaseModel):
    """Model for a quiz question and answer"""
    question: str
    answer: str

class QuizGenerationRequest(BaseModel):
    """Request model for quiz generation"""
    document_id: int
    llm_model: str = "gemini"  # Default to gemini, but can be changed to "gemini"
    num_questions: int = 5  # Default to 5 questions

    class Config:
        schema_extra = {
            "example": {
                "document_id": 1,
                "llm_model": "gemini",
                "num_questions": 5
            }
        }

class QuizGenerationResponse(BaseModel):
    """Response model for quiz generation"""
    message: str
    document_id: int
    questions: List[QuizQuestion]
    status: str
    model: str
    tokens: int

class Flashcard(BaseModel):
    """Model for a flashcard with front and back content"""
    front: str
    back: str

class FlashcardGenerationRequest(BaseModel):
    """Request model for flashcard generation"""
    document_id: int
    llm_model: str = "gemini"  # Default to gemini, but can be changed to "gemini"
    num_flashcards: int = 10  # Default to 10 flashcards

    class Config:
        schema_extra = {
            "example": {
                "document_id": 1,
                "llm_model": "gemini",
                "num_flashcards": 10
            }
        }

class FlashcardGenerationResponse(BaseModel):
    """Response model for flashcard generation"""
    message: str
    document_id: int
    flashcards: List[Flashcard]
    status: str
    model: str
    tokens: int

class SummaryGenerationRequest(BaseModel):
    """Request model for summary generation"""
    document_id: int
    llm_model: str = "gemini"  # Default to Gemini
    summary_type: str = "comprehensive"  # comprehensive, brief, key_points

    class Config:
        schema_extra = {
            "example": {
                "document_id": 1,
                "llm_model": "gemini",
                "summary_type": "comprehensive"
            }
        }

class SummaryGenerationResponse(BaseModel):
    """Response model for summary generation"""
    message: str
    document_id: int
    summary: str
    status: str
    model: str
    tokens: int

class FlowchartGenerationRequest(BaseModel):
    """Request model for flowchart generation"""
    document_id: int
    llm_model: str = "gemini"  # Default to Gemini

    class Config:
        schema_extra = {
            "example": {
                "document_id": 1,
                "llm_model": "gemini"
            }
        }

class FlowchartGenerationResponse(BaseModel):
    """Response model for flowchart generation"""
    message: str
    document_id: int
    flowchart: str
    status: str
    model: str
    tokens: int

class Chapter(BaseModel):
    """Model for a chapter with title and content"""
    title: str
    content: str
    subsections: List[dict] = []

class ChapterGenerationRequest(BaseModel):
    """Request model for chapter generation"""
    document_id: int
    llm_model: str = "gemini"
    num_chapters: int = 5

    class Config:
        schema_extra = {
            "example": {
                "document_id": 1,
                "llm_model": "gemini",
                "num_chapters": 5
            }
        }

class ChapterGenerationResponse(BaseModel):
    """Response model for chapter generation"""
    message: str
    document_id: int
    chapters: List[Chapter]
    status: str
    model: str
    tokens: int


class LearningTimeAnalysis(BaseModel):
    """Model for learning time analysis factors"""
    topic_difficulty: int  # 1-5 scale
    content_length_words: int
    concept_density: int  # 1-5 scale
    additional_factors: dict = {}


class LearningTimePredictionRequest(BaseModel):
    """Request model for learning time prediction"""
    document_id: int
    llm_model: str = "gemini"  # Default to Gemini
    force_regenerate: bool = False  # Force regeneration even if prediction exists

    class Config:
        schema_extra = {
            "example": {
                "document_id": 1,
                "llm_model": "gemini",
                "force_regenerate": False
            }
        }


class LearningTimePredictionResponse(BaseModel):
    """Response model for learning time prediction"""
    message: str
    document_id: int
    predicted_time_seconds: int
    time_range_display: str  # Human-readable time range (e.g., "45m - 1h 15m")
    analysis: LearningTimeAnalysis
    gemini_reasoning: str
    status: str
    model: str
    tokens: int

    class Config:
        schema_extra = {
            "example": {
                "message": "Learning time predicted successfully",
                "document_id": 1,
                "predicted_time_seconds": 3600,
                "time_range_display": "48m - 1h 12m",
                "analysis": {
                    "topic_difficulty": 3,
                    "content_length_words": 2500,
                    "concept_density": 4,
                    "additional_factors": {
                        "subject_area": "mathematics",
                        "formula_count": 15,
                        "example_problems": 8
                    }
                },
                "gemini_reasoning": "This document covers intermediate calculus concepts with multiple formulas and practice problems...",
                "status": "completed",
                "model": "gemini",
                "tokens": 1250
            }
        }

class BlueprintGenerationRequest(BaseModel):
    """Request model for blueprint generation"""
    document_id: int
    llm_model: str = "gemini"  # Default to Gemini
    focus_areas: Optional[str] = None  # Optional focus areas for the blueprint

    class Config:
        schema_extra = {
            "example": {
                "document_id": 1,
                "llm_model": "gemini",
                "focus_areas": "machine learning, data science"
            }
        }

class BlueprintGenerationResponse(BaseModel):
    """Response model for blueprint generation"""
    message: str
    document_id: int
    blueprint: str
    topics: List[BlueprintTopic]
    status: str
    model: str
    tokens: int
