"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/process/page",{

/***/ "(app-pages-browser)/./components/sidebar.tsx":
/*!********************************!*\
  !*** ./components/sidebar.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Clock,CuboidIcon,LogOut,Moon,Plus,Sun,ThumbsUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Clock,CuboidIcon,LogOut,Moon,Plus,Sun,ThumbsUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Clock,CuboidIcon,LogOut,Moon,Plus,Sun,ThumbsUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Clock,CuboidIcon,LogOut,Moon,Plus,Sun,ThumbsUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Clock,CuboidIcon,LogOut,Moon,Plus,Sun,ThumbsUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cuboid.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Clock,CuboidIcon,LogOut,Moon,Plus,Sun,ThumbsUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Clock,CuboidIcon,LogOut,Moon,Plus,Sun,ThumbsUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Clock,CuboidIcon,LogOut,Moon,Plus,Sun,ThumbsUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/thumbs-up.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Clock,CuboidIcon,LogOut,Moon,Plus,Sun,ThumbsUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Clock,CuboidIcon,LogOut,Moon,Plus,Sun,ThumbsUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/theme-provider */ \"(app-pages-browser)/./components/theme-provider.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _lib_navigation_events__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/lib/navigation-events */ \"(app-pages-browser)/./lib/navigation-events.ts\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction Sidebar(param) {\n    let { isOpen, setIsOpen, isLoggedIn = false, username = \"\", onLogout } = param;\n    _s();\n    const { theme, setTheme } = (0,_components_theme_provider__WEBPACK_IMPORTED_MODULE_3__.useTheme)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    // Determine text color based on theme\n    const textColor = theme === \"dark\" ? \"text-neutral-300\" : \"text-neutral-800\";\n    const iconColor = theme === \"dark\" ? \"text-neutral-400\" : \"text-neutral-600\";\n    const goToHome = ()=>{\n        _lib_navigation_events__WEBPACK_IMPORTED_MODULE_5__.navigationEvents.triggerNavigation();\n        router.push(\"/\");\n        setIsOpen(false);\n    };\n    const handleSignIn = ()=>{\n        router.push(\"/auth\");\n        setIsOpen(false);\n    };\n    const handleLogout = ()=>{\n        if (onLogout) {\n            onLogout();\n        }\n        setIsOpen(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.AnimatePresence, {\n                children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 0.5\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    transition: {\n                        duration: 0.2\n                    },\n                    className: \"fixed inset-0 bg-black z-40 md:hidden\",\n                    onClick: ()=>setIsOpen(false)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_7__.motion.div, {\n                initial: {\n                    x: \"-100%\"\n                },\n                animate: {\n                    x: isOpen ? 0 : \"-100%\"\n                },\n                transition: {\n                    duration: 0.3,\n                    ease: \"easeInOut\"\n                },\n                className: \"fixed top-0 left-0 h-full w-64 border-r flex flex-col z-50 \".concat(theme === \"light\" ? \"border-black bg-white\" : \"border-border bg-background\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"flex items-center gap-2 hover:text-purple-500\",\n                                onClick: goToHome,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative h-6 w-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            src: \"https://hebbkx1anhila5yf.public.blob.vercel-storage.com/logo..-modified-dNI2SJo4cpnC8nxN30N6PW6EvffdAE.png\",\n                                            alt: \"Cognimosity Logo\",\n                                            fill: true,\n                                            className: \"object-contain\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 72,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 71,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-semibold\",\n                                        children: \"Cognimosity\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 70,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                className: \"h-8 w-8\",\n                                onClick: ()=>setIsOpen(false),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                        lineNumber: 69,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-3 py-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            variant: \"ghost\",\n                            className: \"w-full justify-start px-4 py-2 text-sm \".concat(textColor, \" hover:text-purple-500\"),\n                            onClick: ()=>setTheme(theme === \"dark\" ? \"light\" : \"dark\"),\n                            children: theme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2 \".concat(iconColor)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 95,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Light Mode\"\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2 \".concat(iconColor)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 100,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Dark Mode\"\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-3 py-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full px-4 py-2 \".concat(textColor, \" font-medium\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"History\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 110,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 109,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pl-6 space-y-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-neutral-500 py-2\",\n                                    children: \"No recent history\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 116,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 115,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-3 py-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full px-4 py-2 \".concat(textColor, \" font-medium\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Spaces\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 125,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 123,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 122,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pl-6 space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        variant: \"ghost\",\n                                        className: \"w-full justify-start px-4 py-2 text-sm \".concat(textColor, \" hover:text-purple-500\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2 \".concat(iconColor)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 133,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"My Space\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 129,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        variant: \"outline\",\n                                        className: \"w-full justify-start gap-2 border-dashed border-neutral-700 text-sm \".concat(textColor, \" hover:border-purple-500 hover:text-purple-500\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Add space\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 136,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 128,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-3 py-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full px-4 py-2 \".concat(textColor, \" font-medium\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Help & Tools\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 149,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pl-6 space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        variant: \"ghost\",\n                                        className: \"w-full justify-start px-4 py-2 text-sm \".concat(textColor, \" hover:text-purple-500\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2 \".concat(iconColor)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 159,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Feedback\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 155,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        variant: \"ghost\",\n                                        className: \"w-full justify-start px-4 py-2 text-sm \".concat(textColor, \" hover:text-purple-500\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2 \".concat(iconColor)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 166,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Quick Guide\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 162,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        variant: \"ghost\",\n                                        className: \"w-full justify-start px-4 py-2 text-sm \".concat(textColor, \" hover:text-purple-500\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2 \".concat(iconColor)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Invite & Earn\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 169,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 154,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                        lineNumber: 147,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-auto p-3 space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                variant: \"outline\",\n                                className: \"w-full justify-center text-purple-500 hover:bg-purple-500/10 \".concat(theme === \"light\" ? \"border-purple-500\" : \"border-purple-500\"),\n                                children: \"Upgrade\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"rounded-md p-2 text-center \".concat(theme === \"light\" ? \"bg-white border border-black\" : \"bg-neutral-900\"),\n                                children: isLoggedIn ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    variant: \"ghost\",\n                                    className: \"w-full justify-center text-sm \".concat(textColor, \" hover:text-purple-500\"),\n                                    onClick: handleLogout,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2 \".concat(iconColor)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 200,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Log out (\",\n                                        username,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 195,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    variant: \"ghost\",\n                                    className: \"w-full justify-center text-sm \".concat(textColor, \" hover:text-purple-500\"),\n                                    onClick: handleSignIn,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2 \".concat(iconColor)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 209,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Sign in / Sign up\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 189,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Sidebar, \"X9pAEMNvvatsKWVsAf27Hbp7QmM=\", false, function() {\n    return [\n        _components_theme_provider__WEBPACK_IMPORTED_MODULE_3__.useTheme,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/sidebar.tsx\n"));

/***/ })

});