// Utility functions for quiz functionality

export interface QuizQuestion {
  question: string
  answer: string
}

export interface QuizAttempt {
  questions: QuizQuestion[]
  userAnswers: string[]
  startTime: number
  endTime: number
}

/**
 * Calculate quiz score based on user answers and correct answers
 */
export function calculateQuizScore(questions: QuizQuestion[], userAnswers: string[]): number {
  if (questions.length === 0 || userAnswers.length === 0) return 0
  
  let correctAnswers = 0
  
  questions.forEach((question, index) => {
    const userAnswer = userAnswers[index]?.toLowerCase().trim() || ''
    const correctAnswer = question.answer.toLowerCase().trim()
    
    // Simple matching - check if user answer contains key terms from correct answer
    // or if correct answer contains user answer
    if (userAnswer && (
      userAnswer.includes(correctAnswer) || 
      correctAnswer.includes(userAnswer) ||
      calculateSimilarity(userAnswer, correctAnswer) > 0.7
    )) {
      correctAnswers++
    }
  })
  
  return (correctAnswers / questions.length) * 100
}

/**
 * Calculate similarity between two strings using simple word matching
 */
function calculateSimilarity(str1: string, str2: string): number {
  const words1 = str1.toLowerCase().split(/\s+/).filter(word => word.length > 2)
  const words2 = str2.toLowerCase().split(/\s+/).filter(word => word.length > 2)
  
  if (words1.length === 0 || words2.length === 0) return 0
  
  const commonWords = words1.filter(word => words2.includes(word))
  return commonWords.length / Math.max(words1.length, words2.length)
}

/**
 * Format time duration in milliseconds to readable format
 */
export function formatDuration(milliseconds: number): string {
  const seconds = Math.floor(milliseconds / 1000)
  const minutes = Math.floor(seconds / 60)
  const hours = Math.floor(minutes / 60)
  
  if (hours > 0) {
    return `${hours}h ${minutes % 60}m ${seconds % 60}s`
  } else if (minutes > 0) {
    return `${minutes}m ${seconds % 60}s`
  } else {
    return `${seconds}s`
  }
}

/**
 * Format time duration in seconds to readable format
 */
export function formatTimeFromSeconds(seconds: number): string {
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = seconds % 60
  
  if (minutes > 0) {
    return `${minutes}m ${remainingSeconds}s`
  } else {
    return `${remainingSeconds}s`
  }
}

/**
 * Get performance level based on score
 */
export function getPerformanceLevel(score: number): {
  level: string
  color: string
  description: string
} {
  if (score >= 90) {
    return {
      level: "Excellent",
      color: "text-foreground",
      description: "Outstanding performance!"
    }
  } else if (score >= 80) {
    return {
      level: "Good",
      color: "text-foreground",
      description: "Well done!"
    }
  } else if (score >= 70) {
    return {
      level: "Average",
      color: "text-muted-foreground",
      description: "Room for improvement"
    }
  } else if (score >= 60) {
    return {
      level: "Below Average",
      color: "text-muted-foreground",
      description: "Needs more practice"
    }
  } else {
    return {
      level: "Poor",
      color: "text-muted-foreground",
      description: "Requires significant improvement"
    }
  }
}

/**
 * Calculate improvement trend from performance history
 */
export function calculateImprovementTrend(scores: number[]): {
  trend: 'improving' | 'declining' | 'stable'
  percentage: number
} {
  if (scores.length < 2) {
    return { trend: 'stable', percentage: 0 }
  }
  
  const firstHalf = scores.slice(0, Math.floor(scores.length / 2))
  const secondHalf = scores.slice(Math.floor(scores.length / 2))
  
  const firstAvg = firstHalf.reduce((sum, score) => sum + score, 0) / firstHalf.length
  const secondAvg = secondHalf.reduce((sum, score) => sum + score, 0) / secondHalf.length
  
  const difference = secondAvg - firstAvg
  const percentage = Math.abs(difference)
  
  if (Math.abs(difference) < 5) {
    return { trend: 'stable', percentage: 0 }
  } else if (difference > 0) {
    return { trend: 'improving', percentage }
  } else {
    return { trend: 'declining', percentage }
  }
}

/**
 * Generate performance insights based on quiz data
 */
export function generatePerformanceInsights(performances: any[]): string[] {
  const insights: string[] = []
  
  if (performances.length === 0) {
    return ["Take your first quiz to see performance insights!"]
  }
  
  const scores = performances.map(p => p.quiz_score)
  const times = performances.map(p => p.time_taken)
  
  const avgScore = scores.reduce((sum, score) => sum + score, 0) / scores.length
  const avgTime = times.reduce((sum, time) => sum + time, 0) / times.length
  
  // Score insights
  if (avgScore >= 85) {
    insights.push("🎉 Excellent overall performance! Keep up the great work.")
  } else if (avgScore >= 70) {
    insights.push("👍 Good performance overall. Focus on areas where you scored lower.")
  } else {
    insights.push("📚 Consider reviewing the material more thoroughly before taking quizzes.")
  }
  
  // Time insights
  if (avgTime < 300) { // Less than 5 minutes
    insights.push("⚡ You're completing quizzes quickly. Make sure you're reading questions carefully.")
  } else if (avgTime > 900) { // More than 15 minutes
    insights.push("🐌 You're taking time to think through answers, which is good for accuracy.")
  }
  
  // Trend insights
  const trend = calculateImprovementTrend(scores)
  if (trend.trend === 'improving') {
    insights.push(`📈 Your scores are improving by ${trend.percentage.toFixed(1)}% on average!`)
  } else if (trend.trend === 'declining') {
    insights.push(`📉 Your recent scores have declined. Consider reviewing the material again.`)
  }
  
  return insights
}
