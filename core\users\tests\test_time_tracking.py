from django.test import TestCase
from django.urls import reverse
from django.utils import timezone
from rest_framework.test import APIClient
from rest_framework.authtoken.models import Token
from users.models import Student, PlatformTimeSession, PlatformTimeStats
from documents.models import Document
import tempfile
import os


class TimeTrackingAPITestCase(TestCase):
    def setUp(self):
        # Create test user
        self.user = Student.objects.create_user(
            username='testuser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        # Create auth token
        self.token = Token.objects.create(user=self.user)
        
        # Create test document
        # Create a temporary file for testing
        self.temp_file = tempfile.NamedTemporaryFile(delete=False, suffix='.txt')
        self.temp_file.write(b'Test document content')
        self.temp_file.close()
        
        with open(self.temp_file.name, 'rb') as f:
            self.document = Document.objects.create(
                user=self.user,
                title='Test Document',
                file=f.name
            )
        
        # Set up API client
        self.client = APIClient()
        self.client.credentials(HTTP_AUTHORIZATION=f'Token {self.token.key}')
    
    def tearDown(self):
        # Clean up temporary file
        if os.path.exists(self.temp_file.name):
            os.unlink(self.temp_file.name)
    
    def test_start_session(self):
        """Test starting a platform session"""
        url = reverse('start-platform-session')
        data = {'document_id': self.document.id}

        response = self.client.post(url, data, format='json')

        if response.status_code != 200:
            print(f"Response status: {response.status_code}")
            print(f"Response data: {response.data}")

        self.assertEqual(response.status_code, 201)
        self.assertIn('session_id', response.data)
        self.assertIn('message', response.data)

        # Check session was created
        session = PlatformTimeSession.objects.get(id=response.data['session_id'])
        self.assertEqual(session.student, self.user)
        self.assertEqual(session.document, self.document)
    
    def test_end_session(self):
        """Test ending a platform session"""
        # Create a session manually
        session = PlatformTimeSession.objects.create(
            student=self.user,
            document=self.document,
            session_start=timezone.now()
        )

        url = reverse('end-platform-session')
        data = {'session_id': session.id}

        response = self.client.post(url, data, format='json')

        self.assertEqual(response.status_code, 200)
        self.assertIn('message', response.data)
        self.assertIn('total_time_seconds', response.data)

        session.refresh_from_db()
        self.assertIsNotNone(session.session_end)

    def test_get_platform_stats(self):
        """Test getting platform statistics"""
        # Create some stats manually
        stats = PlatformTimeStats.objects.create(
            student=self.user,
            document=self.document,
            total_time_seconds=3600,
            total_sessions=1
        )

        url = reverse('get-platform-stats')
        response = self.client.get(url)

        self.assertEqual(response.status_code, 200)
        self.assertIn('overview', response.data)
        self.assertIn('recent_sessions', response.data)
    
    def test_unauthorized_access(self):
        """Test that unauthorized users cannot access endpoints"""
        # Remove authentication
        self.client.credentials()

        url = reverse('start-platform-session')
        data = {'document_id': self.document.id}

        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, 401)
    
    def test_document_ownership(self):
        """Test that users can only track time for their own documents"""
        # Create another user and document
        other_user = Student.objects.create_user(
            username='otheruser',
            email='<EMAIL>',
            password='testpass123'
        )
        
        with open(self.temp_file.name, 'rb') as f:
            other_document = Document.objects.create(
                user=other_user,
                title='Other Document',
                file=f.name
            )
        
        url = reverse('start-platform-session')
        data = {'document_id': other_document.id}
        
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, 404)  # Should not find document
