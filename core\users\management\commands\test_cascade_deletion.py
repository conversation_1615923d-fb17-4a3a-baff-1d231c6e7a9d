from django.core.management.base import BaseCommand
from django.db import transaction
from django.core.files.uploadedfile import SimpleUploadedFile
from users.models import Student, PlatformTimeSession, PlatformTimeStats, PlatformTimeEvent
from documents.models import Document
import datetime
import pytz

class Command(BaseCommand):
    help = 'Test CASCADE deletion functionality'

    def handle(self, *args, **options):
        self.stdout.write("🧪 Testing CASCADE deletion functionality...")
        self.stdout.write("=" * 50)
        
        try:
            with transaction.atomic():
                # Create a test student
                test_student = Student.objects.create(
                    email="<EMAIL>",
                    first_name="Test",
                    last_name="User"
                )
                self.stdout.write(f"✅ Created test student: {test_student.id}")
                
                # Create a test document
                test_file = SimpleUploadedFile(
                    "test_cascade.txt", 
                    b"This is a test document for cascade deletion testing", 
                    content_type="text/plain"
                )
                test_document = Document.objects.create(
                    title="Test Document for CASCADE",
                    file=test_file,
                    user=test_student
                )
                self.stdout.write(f"✅ Created test document: {test_document.id}")
                
                # Create time tracking records
                indian_tz = pytz.timezone('Asia/Kolkata')
                now = indian_tz.localize(datetime.datetime.now().replace(tzinfo=None))
                
                # Create session
                session = PlatformTimeSession.objects.create(
                    student=test_student,
                    document=test_document,
                    session_start=now,
                    total_time_seconds=300
                )
                self.stdout.write(f"✅ Created test session: {session.id}")
                
                # Create stats
                stats = PlatformTimeStats.objects.create(
                    student=test_student,
                    document=test_document,
                    total_time_seconds=300,
                    total_sessions=1,
                    view_count=1,
                    first_access=now,
                    last_access=now
                )
                self.stdout.write(f"✅ Created test stats: {stats.id}")
                
                # Create event
                event = PlatformTimeEvent.objects.create(
                    session=session,
                    event_type='start',
                    timestamp=now
                )
                self.stdout.write(f"✅ Created test event: {event.id}")
                
                # Verify records exist
                self.stdout.write("\n📊 Records before deletion:")
                self.stdout.write(f"   - PlatformTimeSession: {PlatformTimeSession.objects.filter(document=test_document).count()}")
                self.stdout.write(f"   - PlatformTimeStats: {PlatformTimeStats.objects.filter(document=test_document).count()}")
                self.stdout.write(f"   - PlatformTimeEvent: {PlatformTimeEvent.objects.filter(session__document=test_document).count()}")
                
                # Now test CASCADE deletion by deleting the document
                self.stdout.write(f"\n🗑️  Deleting document {test_document.id}...")
                test_document.delete()
                
                # Check if related records were automatically deleted
                self.stdout.write("\n📊 Records after deletion:")
                sessions_count = PlatformTimeSession.objects.filter(student=test_student).count()
                stats_count = PlatformTimeStats.objects.filter(student=test_student).count()
                events_count = PlatformTimeEvent.objects.filter(session__student=test_student).count()
                
                self.stdout.write(f"   - PlatformTimeSession: {sessions_count}")
                self.stdout.write(f"   - PlatformTimeStats: {stats_count}")
                self.stdout.write(f"   - PlatformTimeEvent: {events_count}")
                
                if sessions_count == 0 and stats_count == 0 and events_count == 0:
                    self.stdout.write("\n✅ CASCADE deletion is working correctly!")
                    self.stdout.write("   All related time tracking records were automatically deleted")
                else:
                    self.stdout.write("\n❌ CASCADE deletion is NOT working!")
                    self.stdout.write("   Some related records were not deleted")
                
                # Clean up test student
                test_student.delete()
                self.stdout.write(f"🧹 Cleaned up test student")
                
                # Rollback the transaction to avoid leaving test data
                raise Exception("Rolling back test transaction")
                
        except Exception as e:
            if "Rolling back test transaction" in str(e):
                self.stdout.write("\n🔄 Test completed (transaction rolled back)")
            else:
                self.stdout.write(f"\n❌ Test failed: {e}")
                raise
        
        self.stdout.write("\n🎉 CASCADE deletion test completed!")
