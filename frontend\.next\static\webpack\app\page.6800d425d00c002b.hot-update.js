"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/cogni-ui.tsx":
/*!*********************************!*\
  !*** ./components/cogni-ui.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CogniUI: () => (/* binding */ CogniUI)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Crown,CuboidIcon,FileText,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Crown,CuboidIcon,FileText,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Crown,CuboidIcon,FileText,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Crown,CuboidIcon,FileText,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Crown,CuboidIcon,FileText,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Crown,CuboidIcon,FileText,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Crown,CuboidIcon,FileText,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Crown,CuboidIcon,FileText,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Crown,CuboidIcon,FileText,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Crown,CuboidIcon,FileText,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cuboid.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Crown,CuboidIcon,FileText,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Crown,CuboidIcon,FileText,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Crown,CuboidIcon,FileText,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Crown,CuboidIcon,FileText,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/sidebar */ \"(app-pages-browser)/./components/sidebar.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/theme-provider */ \"(app-pages-browser)/./components/theme-provider.tsx\");\n/* harmony import */ var _components_create_space_dialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/create-space-dialog */ \"(app-pages-browser)/./components/create-space-dialog.tsx\");\n/* harmony import */ var _components_upload_modal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/upload-modal */ \"(app-pages-browser)/./components/upload-modal.tsx\");\n/* harmony import */ var _components_paste_modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/paste-modal */ \"(app-pages-browser)/./components/paste-modal.tsx\");\n/* harmony import */ var _components_record_modal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/record-modal */ \"(app-pages-browser)/./components/record-modal.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ CogniUI auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction CogniUI() {\n    var _groups_find;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const { theme, setTheme } = (0,_components_theme_provider__WEBPACK_IMPORTED_MODULE_6__.useTheme)();\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [createSpaceOpen, setCreateSpaceOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadModalOpen, setUploadModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [pasteModalOpen, setPasteModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [recordModalOpen, setRecordModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Mock user state - in a real app, this would come from your auth system\n    const [isLoggedIn, setIsLoggedIn] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Documents state\n    const [documents, setDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [documentsLoading, setDocumentsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [documentsError, setDocumentsError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [groups, setGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedGroup, setSelectedGroup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleUpgradeClick = ()=>{\n        router.push(\"/subscription\");\n    };\n    const handleSignIn = ()=>{\n        router.push(\"/auth\");\n    };\n    const handleLogout = ()=>{\n        // Clear localStorage\n        localStorage.removeItem(\"isLoggedIn\");\n        localStorage.removeItem(\"username\");\n        localStorage.removeItem(\"token\");\n        setIsLoggedIn(false);\n        setUsername(\"\");\n        setDocuments([]);\n        router.push(\"/\");\n    };\n    // Function to fetch user documents\n    const fetchDocuments = async ()=>{\n        if (!isLoggedIn) return;\n        setDocumentsLoading(true);\n        setDocumentsError(null);\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_12__.documentApi.getDocuments();\n            setDocuments(response.results || response || []);\n        } catch (error) {\n            console.error('Error fetching documents:', error);\n            setDocumentsError('Failed to load documents');\n        } finally{\n            setDocumentsLoading(false);\n        }\n    };\n    // Handle document click - navigate to learning page\n    const handleDocumentClick = (document)=>{\n        if (document.processing_status === 'completed') {\n            router.push(\"/process?documentId=\".concat(document.id, \"&type=upload\"));\n        }\n    };\n    // Check auth state on mount\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"CogniUI.useEffect\": ()=>{\n            const userLoggedIn = localStorage.getItem(\"isLoggedIn\") === \"true\";\n            const storedUsername = localStorage.getItem(\"username\");\n            if (userLoggedIn && storedUsername) {\n                setIsLoggedIn(true);\n                setUsername(storedUsername);\n            }\n        }\n    }[\"CogniUI.useEffect\"], []);\n    // Fetch documents when user logs in\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CogniUI.useEffect\": ()=>{\n            if (isLoggedIn) {\n                fetchDocuments();\n            }\n        }\n    }[\"CogniUI.useEffect\"], [\n        isLoggedIn\n    ]);\n    // Fetch groups when user logs in\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CogniUI.useEffect\": ()=>{\n            const fetchGroups = {\n                \"CogniUI.useEffect.fetchGroups\": async ()=>{\n                    try {\n                        const groupsData = await _lib_api__WEBPACK_IMPORTED_MODULE_12__.documentApi.getGroups();\n                        // Ensure groups is always an array\n                        setGroups(Array.isArray(groupsData) ? groupsData : groupsData.results || []);\n                    } catch (error) {\n                        console.error(\"Failed to fetch groups:\", error);\n                        setGroups([]);\n                    }\n                }\n            }[\"CogniUI.useEffect.fetchGroups\"];\n            if (isLoggedIn) {\n                fetchGroups();\n            }\n        }\n    }[\"CogniUI.useEffect\"], [\n        isLoggedIn\n    ]);\n    // Fetch documents when user logs in or selectedGroup changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CogniUI.useEffect\": ()=>{\n            const fetchDocumentsByGroup = {\n                \"CogniUI.useEffect.fetchDocumentsByGroup\": async ()=>{\n                    setDocumentsLoading(true);\n                    setDocumentsError(null);\n                    try {\n                        const response = await _lib_api__WEBPACK_IMPORTED_MODULE_12__.documentApi.getDocuments(selectedGroup || undefined);\n                        setDocuments(response.results || response || []);\n                    } catch (error) {\n                        console.error('Error fetching documents:', error);\n                        setDocumentsError('Failed to load documents');\n                    } finally{\n                        setDocumentsLoading(false);\n                    }\n                }\n            }[\"CogniUI.useEffect.fetchDocumentsByGroup\"];\n            if (isLoggedIn) {\n                fetchDocumentsByGroup();\n            }\n        }\n    }[\"CogniUI.useEffect\"], [\n        isLoggedIn,\n        selectedGroup\n    ]);\n    const handleCreateGroup = ()=>{\n        setCreateSpaceOpen(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-background text-foreground overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sidebar__WEBPACK_IMPORTED_MODULE_3__.Sidebar, {\n                isOpen: sidebarOpen,\n                setIsOpen: setSidebarOpen,\n                isLoggedIn: isLoggedIn,\n                username: username,\n                onLogout: handleLogout\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                lineNumber: 166,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                className: \"h-10 w-10 rounded-full\",\n                                onClick: ()=>setSidebarOpen(true),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                    lineNumber: 178,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                lineNumber: 177,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    isLoggedIn ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenu, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenuTrigger, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"outline\",\n                                                    className: \"border-2 border-gray-300 hover:border-gray-500 bg-white hover:bg-gray-50 text-gray-900 dark:bg-gray-900 dark:text-gray-100 dark:border-gray-600 dark:hover:border-gray-400 dark:hover:bg-gray-800\",\n                                                    children: username\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 185,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 184,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenuContent, {\n                                                align: \"end\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenuItem, {\n                                                    onClick: handleLogout,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                            lineNumber: 189,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Log out\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                            lineNumber: 190,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 183,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        className: \"bg-purple-600 hover:bg-purple-700\",\n                                        onClick: handleSignIn,\n                                        children: \"Sign In / Register\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 195,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        className: \"bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white border-0\",\n                                        size: \"sm\",\n                                        onClick: handleUpgradeClick,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Upgrade\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 201,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        className: \"h-10 w-10 rounded-full\",\n                                        onClick: ()=>setTheme(theme === \"dark\" ? \"light\" : \"dark\"),\n                                        children: theme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 35\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                            lineNumber: 217,\n                                            columnNumber: 65\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                        lineNumber: 176,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-5xl mx-auto px-4 py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mb-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative h-16 w-16\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        src: \"https://hebbkx1anhila5yf.public.blob.vercel-storage.com/logo..-modified-dNI2SJo4cpnC8nxN30N6PW6EvffdAE.png\",\n                                        alt: \"Cognimosity Logo\",\n                                        fill: true,\n                                        className: \"object-contain\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                    lineNumber: 224,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                lineNumber: 223,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl font-bold text-center mb-12\",\n                                children: \"Ready to unlock something new today?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                lineNumber: 233,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setUploadModalOpen(true),\n                                        className: \"border rounded-lg p-6 flex flex-col items-center transition-all duration-300 hover:border-purple-500 hover:shadow-[0_0_15px_rgba(168,85,247,0.3)] group relative \".concat(theme === \"light\" ? \"border-black bg-white\" : \"border-neutral-800\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-2 right-2 bg-purple-500 text-xs px-2 py-0.5 rounded-full\",\n                                                children: \"Popular\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 242,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4 p-3 rounded-full transition-all duration-300 \".concat(theme === \"light\" ? \"bg-white border border-black\" : \"bg-neutral-900\", \" group-hover:bg-purple-500/20\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-6 w-6 group-hover:text-purple-500 transition-all duration-300 \".concat(theme === \"light\" ? \"text-black\" : \"\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 248,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium mb-1\",\n                                                children: \"Upload\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-center \".concat(theme === \"light\" ? \"text-neutral-600\" : \"text-neutral-400\"),\n                                                children: \"PDF, PPT, DOC, TXT, AUDIO\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setPasteModalOpen(true),\n                                        className: \"border rounded-lg p-6 flex flex-col items-center transition-all duration-300 hover:border-purple-500 hover:shadow-[0_0_15px_rgba(168,85,247,0.3)] group \".concat(theme === \"light\" ? \"border-black bg-white\" : \"border-neutral-800\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4 p-3 rounded-full transition-all duration-300 \".concat(theme === \"light\" ? \"bg-white border border-black\" : \"bg-neutral-900\", \" group-hover:bg-purple-500/20\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-6 w-6 group-hover:text-purple-500 transition-all duration-300 \".concat(theme === \"light\" ? \"text-black\" : \"\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 269,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 264,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium mb-1\",\n                                                children: \"Paste\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-center \".concat(theme === \"light\" ? \"text-neutral-600\" : \"text-neutral-400\"),\n                                                children: \"YouTube, Website, Text\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 274,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setRecordModalOpen(true),\n                                        className: \"border rounded-lg p-6 flex flex-col items-center transition-all duration-300 hover:border-purple-500 hover:shadow-[0_0_15px_rgba(168,85,247,0.3)] group \".concat(theme === \"light\" ? \"border-black bg-white\" : \"border-neutral-800\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4 p-3 rounded-full transition-all duration-300 \".concat(theme === \"light\" ? \"bg-white border border-black\" : \"bg-neutral-900\", \" group-hover:bg-purple-500/20\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"h-6 w-6 group-hover:text-purple-500 transition-all duration-300 \".concat(theme === \"light\" ? \"text-black\" : \"\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 290,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium mb-1\",\n                                                children: \"Record\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 294,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-center \".concat(theme === \"light\" ? \"text-neutral-600\" : \"text-neutral-400\"),\n                                                children: \"Record Your Lecture\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 295,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 279,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-semibold\",\n                                                children: \"My spaces\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 303,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                className: \"gap-2 border-dashed hover:border-purple-500 transition-all duration-300\",\n                                                onClick: handleCreateGroup,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                        lineNumber: 309,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Add space\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 304,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 302,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: groups.length > 0 ? groups.map((group)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border rounded-lg p-4 flex items-center justify-between hover:border-purple-500 transition-all duration-300 cursor-pointer \".concat(selectedGroup === group.id ? 'border-purple-500 bg-purple-50' : theme === \"light\" ? \"border-black bg-white\" : \"border-neutral-800\"),\n                                                onClick: ()=>setSelectedGroup(group.id),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                lineNumber: 324,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: group.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                lineNumber: 325,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                        lineNumber: 323,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-400\",\n                                                        children: new Date(group.created_at).toLocaleDateString()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, group.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 19\n                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border rounded-lg p-4 text-center \".concat(theme === \"light\" ? \"border-gray-200 bg-gray-50\" : \"border-neutral-700 bg-neutral-800\"),\n                                            children: \"No spaces yet. Create one to get started!\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                            lineNumber: 331,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                lineNumber: 301,\n                                columnNumber: 11\n                            }, this),\n                            isLoggedIn && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: selectedGroup === null ? \"default\" : \"outline\",\n                                                    onClick: ()=>setSelectedGroup(null),\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                            lineNumber: 348,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"All Documents\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 19\n                                                }, this),\n                                                groups.map((group)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: selectedGroup === group.id ? \"default\" : \"outline\",\n                                                        onClick: ()=>setSelectedGroup(group.id),\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                lineNumber: 358,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            group.name\n                                                        ]\n                                                    }, group.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                        lineNumber: 352,\n                                                        columnNumber: 21\n                                                    }, this)),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"outline\",\n                                                    className: \"gap-2 border-dashed hover:border-purple-500 transition-all duration-300\",\n                                                    onClick: handleCreateGroup,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                            lineNumber: 367,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Add space\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 362,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                            lineNumber: 342,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-xl font-semibold\",\n                                                            children: selectedGroup ? \"Documents in \".concat((_groups_find = groups.find((g)=>g.id === selectedGroup)) === null || _groups_find === void 0 ? void 0 : _groups_find.name) : 'All Documents'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                    variant: \"ghost\",\n                                                                    size: \"sm\",\n                                                                    onClick: fetchDocuments,\n                                                                    disabled: documentsLoading,\n                                                                    children: documentsLoading ? \"Loading...\" : \"Refresh\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                    lineNumber: 380,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                    className: \"bg-purple-600 hover:bg-purple-700\",\n                                                                    onClick: ()=>setUploadModalOpen(true),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                            className: \"h-4 w-4 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                            lineNumber: 392,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \" Upload Document\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                    lineNumber: 388,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                            lineNumber: 379,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 19\n                                                }, this),\n                                                documentsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4 p-4 bg-red-50 border border-red-200 rounded-lg text-red-700\",\n                                                    children: documentsError\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 397,\n                                                    columnNumber: 21\n                                                }, this),\n                                                documentsLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                    children: [\n                                                        1,\n                                                        2,\n                                                        3\n                                                    ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"border rounded-lg p-4 animate-pulse \".concat(theme === \"light\" ? \"border-gray-200 bg-gray-50\" : \"border-neutral-700 bg-neutral-800\"),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-4 bg-gray-300 rounded mb-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                    lineNumber: 410,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-3 bg-gray-200 rounded mb-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                    lineNumber: 411,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-3 bg-gray-200 rounded w-2/3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                    lineNumber: 412,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, i, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                            lineNumber: 404,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 402,\n                                                    columnNumber: 21\n                                                }, this) : documents.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                    children: documents.map((document)=>{\n                                                        var _groups_find;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            onClick: ()=>handleDocumentClick(document),\n                                                            className: \"border rounded-lg p-4 transition-all duration-300 hover:shadow-lg \".concat(document.processing_status === 'completed' ? 'cursor-pointer hover:border-purple-500 hover:shadow-[0_0_15px_rgba(168,85,247,0.3)]' : 'cursor-not-allowed opacity-60', \" \").concat(theme === \"light\" ? \"border-black bg-white\" : \"border-neutral-800\"),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-start justify-between mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                                    className: \"h-5 w-5 text-purple-500\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                                    lineNumber: 432,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-medium text-sm truncate\",\n                                                                                    children: document.title\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                                    lineNumber: 433,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                            lineNumber: 431,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2\",\n                                                                            children: [\n                                                                                document.group && !selectedGroup && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs px-2 py-0.5 rounded-full bg-purple-100 text-purple-800\",\n                                                                                    children: ((_groups_find = groups.find((g)=>g.id === Number(document.group))) === null || _groups_find === void 0 ? void 0 : _groups_find.name) || 'Group'\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                                    lineNumber: 437,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                document.processing_status === 'completed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                    className: \"h-4 w-4 text-green-500\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                                    lineNumber: 442,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                document.processing_status === 'processing' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                    className: \"h-4 w-4 text-yellow-500 animate-spin\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                                    lineNumber: 445,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                document.processing_status === 'failed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                    className: \"h-4 w-4 text-red-500\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                                    lineNumber: 448,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                            lineNumber: 435,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                    lineNumber: 430,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs mb-2 \".concat(theme === \"light\" ? \"text-gray-600\" : \"text-gray-400\"),\n                                                                    children: [\n                                                                        \"Uploaded: \",\n                                                                        new Date(document.uploaded_at).toLocaleDateString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                    lineNumber: 452,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs px-2 py-1 rounded-full \".concat(document.processing_status === 'completed' ? 'bg-green-100 text-green-800' : document.processing_status === 'processing' ? 'bg-yellow-100 text-yellow-800' : document.processing_status === 'failed' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'),\n                                                                            children: document.processing_status.charAt(0).toUpperCase() + document.processing_status.slice(1)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                            lineNumber: 456,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        document.processing_status === 'completed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs \".concat(theme === \"light\" ? \"text-gray-500\" : \"text-gray-400\"),\n                                                                            children: \"Click to learn\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                            lineNumber: 468,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                    lineNumber: 455,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, document.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                            lineNumber: 419,\n                                                            columnNumber: 25\n                                                        }, this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 417,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border rounded-lg p-8 text-center \".concat(theme === \"light\" ? \"border-gray-200 bg-gray-50\" : \"border-neutral-700 bg-neutral-800\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                            className: \"h-12 w-12 mx-auto mb-4 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                            lineNumber: 480,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-medium mb-2\",\n                                                            children: [\n                                                                \"No documents \",\n                                                                selectedGroup ? 'in this space' : 'yet'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                            lineNumber: 481,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm mb-4 \".concat(theme === \"light\" ? \"text-gray-600\" : \"text-gray-400\"),\n                                                            children: \"Upload your first document to get started with learning\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                            lineNumber: 482,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            onClick: ()=>setUploadModalOpen(true),\n                                                            className: \"bg-purple-600 hover:bg-purple-700\",\n                                                            children: \"Upload Document\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                            lineNumber: 485,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 477,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                    lineNumber: 341,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                lineNumber: 340,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-semibold\",\n                                                children: \"Continue learning\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 500,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"link\",\n                                                className: \"text-neutral-400\",\n                                                children: \"View all\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 501,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 499,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border rounded-lg overflow-hidden hover:border-purple-500 transition-all duration-300 \".concat(theme === \"light\" ? \"border-black bg-white\" : \"border-neutral-800\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-32 bg-purple-900 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-4xl font-bold text-white\",\n                                                        children: \"MAP\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                        lineNumber: 513,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 512,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 507,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border rounded-lg overflow-hidden hover:border-purple-500 transition-all duration-300 \".concat(theme === \"light\" ? \"border-black bg-white\" : \"border-neutral-800\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-32 \".concat(theme === \"light\" ? \"bg-gray-100\" : \"bg-neutral-900\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 522,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 517,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border rounded-lg overflow-hidden hover:border-purple-500 transition-all duration-300 \".concat(theme === \"light\" ? \"border-black bg-white\" : \"border-neutral-800\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-32 \".concat(theme === \"light\" ? \"bg-gray-100\" : \"bg-neutral-900\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 530,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 525,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 506,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                lineNumber: 498,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                        lineNumber: 222,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_upload_modal__WEBPACK_IMPORTED_MODULE_8__.UploadModal, {\n                isOpen: uploadModalOpen,\n                setIsOpen: setUploadModalOpen,\n                onUploadSuccess: fetchDocuments,\n                groupId: selectedGroup,\n                groups: groups\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                lineNumber: 538,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_paste_modal__WEBPACK_IMPORTED_MODULE_9__.PasteModal, {\n                isOpen: pasteModalOpen,\n                setIsOpen: setPasteModalOpen\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                lineNumber: 545,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_record_modal__WEBPACK_IMPORTED_MODULE_10__.RecordModal, {\n                isOpen: recordModalOpen,\n                setIsOpen: setRecordModalOpen\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                lineNumber: 546,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_create_space_dialog__WEBPACK_IMPORTED_MODULE_7__.CreateSpaceDialog, {\n                open: createSpaceOpen,\n                setOpen: setCreateSpaceOpen,\n                onCreated: ()=>{\n                    // Refresh groups after creation\n                    (async ()=>{\n                        try {\n                            const groupsData = await _lib_api__WEBPACK_IMPORTED_MODULE_12__.documentApi.getGroups();\n                            setGroups(Array.isArray(groupsData) ? groupsData : groupsData.results || []);\n                        } catch (error) {\n                            setGroups([]);\n                        }\n                    })();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                lineNumber: 549,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n        lineNumber: 164,\n        columnNumber: 5\n    }, this);\n}\n_s(CogniUI, \"qKnYNDzkefjcMbBSEb3k81a2SnU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter,\n        _components_theme_provider__WEBPACK_IMPORTED_MODULE_6__.useTheme\n    ];\n});\n_c = CogniUI;\nvar _c;\n$RefreshReg$(_c, \"CogniUI\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/cogni-ui.tsx\n"));

/***/ })

});