"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/cogni-ui.tsx":
/*!*********************************!*\
  !*** ./components/cogni-ui.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CogniUI: () => (/* binding */ CogniUI)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Crown,CuboidIcon,FileText,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Crown,CuboidIcon,FileText,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Crown,CuboidIcon,FileText,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Crown,CuboidIcon,FileText,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Crown,CuboidIcon,FileText,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Crown,CuboidIcon,FileText,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Crown,CuboidIcon,FileText,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Crown,CuboidIcon,FileText,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Crown,CuboidIcon,FileText,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Crown,CuboidIcon,FileText,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cuboid.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Crown,CuboidIcon,FileText,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Crown,CuboidIcon,FileText,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Crown,CuboidIcon,FileText,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Crown,CuboidIcon,FileText,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/sidebar */ \"(app-pages-browser)/./components/sidebar.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/theme-provider */ \"(app-pages-browser)/./components/theme-provider.tsx\");\n/* harmony import */ var _components_create_space_dialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/create-space-dialog */ \"(app-pages-browser)/./components/create-space-dialog.tsx\");\n/* harmony import */ var _components_upload_modal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/upload-modal */ \"(app-pages-browser)/./components/upload-modal.tsx\");\n/* harmony import */ var _components_paste_modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/paste-modal */ \"(app-pages-browser)/./components/paste-modal.tsx\");\n/* harmony import */ var _components_record_modal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/record-modal */ \"(app-pages-browser)/./components/record-modal.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _lib_navigation_events__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/navigation-events */ \"(app-pages-browser)/./lib/navigation-events.ts\");\n/* __next_internal_client_entry_do_not_use__ CogniUI auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction CogniUI() {\n    var _groups_find;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const { theme, setTheme } = (0,_components_theme_provider__WEBPACK_IMPORTED_MODULE_6__.useTheme)();\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [createSpaceOpen, setCreateSpaceOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadModalOpen, setUploadModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [pasteModalOpen, setPasteModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [recordModalOpen, setRecordModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Mock user state - in a real app, this would come from your auth system\n    const [isLoggedIn, setIsLoggedIn] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Documents state\n    const [documents, setDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [documentsLoading, setDocumentsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [documentsError, setDocumentsError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [groups, setGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedGroup, setSelectedGroup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleUpgradeClick = ()=>{\n        _lib_navigation_events__WEBPACK_IMPORTED_MODULE_13__.navigationEvents.triggerNavigation();\n        router.push(\"/subscription\");\n    };\n    const handleSignIn = ()=>{\n        _lib_navigation_events__WEBPACK_IMPORTED_MODULE_13__.navigationEvents.triggerNavigation();\n        router.push(\"/auth\");\n    };\n    const handleLogout = ()=>{\n        // Clear localStorage\n        localStorage.removeItem(\"isLoggedIn\");\n        localStorage.removeItem(\"username\");\n        localStorage.removeItem(\"token\");\n        setIsLoggedIn(false);\n        setUsername(\"\");\n        setDocuments([]);\n        _lib_navigation_events__WEBPACK_IMPORTED_MODULE_13__.navigationEvents.triggerNavigation();\n        router.push(\"/\");\n    };\n    // Function to fetch user documents\n    const fetchDocuments = async ()=>{\n        if (!isLoggedIn) return;\n        setDocumentsLoading(true);\n        setDocumentsError(null);\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_12__.documentApi.getDocuments();\n            setDocuments(response.results || response || []);\n        } catch (error) {\n            console.error('Error fetching documents:', error);\n            setDocumentsError('Failed to load documents');\n        } finally{\n            setDocumentsLoading(false);\n        }\n    };\n    // Handle document click - navigate to learning page\n    const handleDocumentClick = (document)=>{\n        if (document.processing_status === 'completed') {\n            router.push(\"/process?documentId=\".concat(document.id, \"&type=upload\"));\n        }\n    };\n    // Check auth state on mount\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"CogniUI.useEffect\": ()=>{\n            const userLoggedIn = localStorage.getItem(\"isLoggedIn\") === \"true\";\n            const storedUsername = localStorage.getItem(\"username\");\n            if (userLoggedIn && storedUsername) {\n                setIsLoggedIn(true);\n                setUsername(storedUsername);\n            }\n        }\n    }[\"CogniUI.useEffect\"], []);\n    // Fetch documents when user logs in\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CogniUI.useEffect\": ()=>{\n            if (isLoggedIn) {\n                fetchDocuments();\n            }\n        }\n    }[\"CogniUI.useEffect\"], [\n        isLoggedIn\n    ]);\n    // Fetch groups when user logs in\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CogniUI.useEffect\": ()=>{\n            const fetchGroups = {\n                \"CogniUI.useEffect.fetchGroups\": async ()=>{\n                    try {\n                        const groupsData = await _lib_api__WEBPACK_IMPORTED_MODULE_12__.documentApi.getGroups();\n                        // Ensure groups is always an array\n                        setGroups(Array.isArray(groupsData) ? groupsData : groupsData.results || []);\n                    } catch (error) {\n                        console.error(\"Failed to fetch groups:\", error);\n                        setGroups([]);\n                    }\n                }\n            }[\"CogniUI.useEffect.fetchGroups\"];\n            if (isLoggedIn) {\n                fetchGroups();\n            }\n        }\n    }[\"CogniUI.useEffect\"], [\n        isLoggedIn\n    ]);\n    // Fetch documents when user logs in or selectedGroup changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CogniUI.useEffect\": ()=>{\n            const fetchDocumentsByGroup = {\n                \"CogniUI.useEffect.fetchDocumentsByGroup\": async ()=>{\n                    setDocumentsLoading(true);\n                    setDocumentsError(null);\n                    try {\n                        const response = await _lib_api__WEBPACK_IMPORTED_MODULE_12__.documentApi.getDocuments(selectedGroup || undefined);\n                        setDocuments(response.results || response || []);\n                    } catch (error) {\n                        console.error('Error fetching documents:', error);\n                        setDocumentsError('Failed to load documents');\n                    } finally{\n                        setDocumentsLoading(false);\n                    }\n                }\n            }[\"CogniUI.useEffect.fetchDocumentsByGroup\"];\n            if (isLoggedIn) {\n                fetchDocumentsByGroup();\n            }\n        }\n    }[\"CogniUI.useEffect\"], [\n        isLoggedIn,\n        selectedGroup\n    ]);\n    const handleCreateGroup = ()=>{\n        setCreateSpaceOpen(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-background text-foreground overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sidebar__WEBPACK_IMPORTED_MODULE_3__.Sidebar, {\n                isOpen: sidebarOpen,\n                setIsOpen: setSidebarOpen,\n                isLoggedIn: isLoggedIn,\n                username: username,\n                onLogout: handleLogout\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                lineNumber: 169,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                className: \"h-10 w-10 rounded-full\",\n                                onClick: ()=>setSidebarOpen(true),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                    lineNumber: 181,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                lineNumber: 180,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    isLoggedIn ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenu, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenuTrigger, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"outline\",\n                                                    className: \"border-2 border-gray-300 hover:border-gray-500 bg-white hover:bg-gray-50 text-gray-900 dark:bg-gray-900 dark:text-gray-100 dark:border-gray-600 dark:hover:border-gray-400 dark:hover:bg-gray-800\",\n                                                    children: username\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 188,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 187,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenuContent, {\n                                                align: \"end\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenuItem, {\n                                                    onClick: handleLogout,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                            lineNumber: 192,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Log out\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                            lineNumber: 193,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 190,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        className: \"bg-purple-600 hover:bg-purple-700\",\n                                        onClick: handleSignIn,\n                                        children: \"Sign In / Register\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 198,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        className: \"bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white border-0\",\n                                        size: \"sm\",\n                                        onClick: handleUpgradeClick,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 209,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Upgrade\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        className: \"h-10 w-10 rounded-full\",\n                                        onClick: ()=>setTheme(theme === \"dark\" ? \"light\" : \"dark\"),\n                                        children: theme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 35\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                            lineNumber: 220,\n                                            columnNumber: 65\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 214,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                        lineNumber: 179,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-5xl mx-auto px-4 py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mb-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative h-16 w-16\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        src: \"https://hebbkx1anhila5yf.public.blob.vercel-storage.com/logo..-modified-dNI2SJo4cpnC8nxN30N6PW6EvffdAE.png\",\n                                        alt: \"Cognimosity Logo\",\n                                        fill: true,\n                                        className: \"object-contain\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 228,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                    lineNumber: 227,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                lineNumber: 226,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl font-bold text-center mb-12\",\n                                children: \"Ready to unlock something new today?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                lineNumber: 236,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setUploadModalOpen(true),\n                                        className: \"border rounded-lg p-6 flex flex-col items-center transition-all duration-300 hover:border-purple-500 hover:shadow-[0_0_15px_rgba(168,85,247,0.3)] group relative \".concat(theme === \"light\" ? \"border-black bg-white\" : \"border-neutral-800\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-2 right-2 bg-purple-500 text-xs px-2 py-0.5 rounded-full\",\n                                                children: \"Popular\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4 p-3 rounded-full transition-all duration-300 \".concat(theme === \"light\" ? \"bg-white border border-black\" : \"bg-neutral-900\", \" group-hover:bg-purple-500/20\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-6 w-6 group-hover:text-purple-500 transition-all duration-300 \".concat(theme === \"light\" ? \"text-black\" : \"\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 251,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium mb-1\",\n                                                children: \"Upload\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 255,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-center \".concat(theme === \"light\" ? \"text-neutral-600\" : \"text-neutral-400\"),\n                                                children: \"PDF, PPT, DOC, TXT, AUDIO\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 239,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setPasteModalOpen(true),\n                                        className: \"border rounded-lg p-6 flex flex-col items-center transition-all duration-300 hover:border-purple-500 hover:shadow-[0_0_15px_rgba(168,85,247,0.3)] group \".concat(theme === \"light\" ? \"border-black bg-white\" : \"border-neutral-800\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4 p-3 rounded-full transition-all duration-300 \".concat(theme === \"light\" ? \"bg-white border border-black\" : \"bg-neutral-900\", \" group-hover:bg-purple-500/20\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"h-6 w-6 group-hover:text-purple-500 transition-all duration-300 \".concat(theme === \"light\" ? \"text-black\" : \"\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 272,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 267,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium mb-1\",\n                                                children: \"Paste\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 276,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-center \".concat(theme === \"light\" ? \"text-neutral-600\" : \"text-neutral-400\"),\n                                                children: \"YouTube, Website, Text\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 261,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setRecordModalOpen(true),\n                                        className: \"border rounded-lg p-6 flex flex-col items-center transition-all duration-300 hover:border-purple-500 hover:shadow-[0_0_15px_rgba(168,85,247,0.3)] group \".concat(theme === \"light\" ? \"border-black bg-white\" : \"border-neutral-800\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4 p-3 rounded-full transition-all duration-300 \".concat(theme === \"light\" ? \"bg-white border border-black\" : \"bg-neutral-900\", \" group-hover:bg-purple-500/20\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-6 w-6 group-hover:text-purple-500 transition-all duration-300 \".concat(theme === \"light\" ? \"text-black\" : \"\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 293,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium mb-1\",\n                                                children: \"Record\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-center \".concat(theme === \"light\" ? \"text-neutral-600\" : \"text-neutral-400\"),\n                                                children: \"Record Your Lecture\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                lineNumber: 238,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-semibold\",\n                                                children: \"My spaces\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 306,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                className: \"gap-2 border-dashed hover:border-purple-500 transition-all duration-300\",\n                                                onClick: handleCreateGroup,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Add space\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 305,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: groups.length > 0 ? groups.map((group)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border rounded-lg p-4 flex items-center justify-between hover:border-purple-500 transition-all duration-300 cursor-pointer \".concat(selectedGroup === group.id ? 'border-purple-500 bg-purple-50' : theme === \"light\" ? \"border-black bg-white\" : \"border-neutral-800\"),\n                                                onClick: ()=>setSelectedGroup(group.id),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                lineNumber: 327,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: group.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                        lineNumber: 326,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-400\",\n                                                        children: new Date(group.created_at).toLocaleDateString()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                        lineNumber: 330,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, group.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 19\n                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border rounded-lg p-4 text-center \".concat(theme === \"light\" ? \"border-gray-200 bg-gray-50\" : \"border-neutral-700 bg-neutral-800\"),\n                                            children: \"No spaces yet. Create one to get started!\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                            lineNumber: 334,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 11\n                            }, this),\n                            isLoggedIn && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: selectedGroup === null ? \"default\" : \"outline\",\n                                                    onClick: ()=>setSelectedGroup(null),\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                            lineNumber: 351,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"All Documents\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 346,\n                                                    columnNumber: 19\n                                                }, this),\n                                                groups.map((group)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: selectedGroup === group.id ? \"default\" : \"outline\",\n                                                        onClick: ()=>setSelectedGroup(group.id),\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                lineNumber: 361,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            group.name\n                                                        ]\n                                                    }, group.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                        lineNumber: 355,\n                                                        columnNumber: 21\n                                                    }, this)),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"outline\",\n                                                    className: \"gap-2 border-dashed hover:border-purple-500 transition-all duration-300\",\n                                                    onClick: handleCreateGroup,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                            lineNumber: 370,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Add space\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                            lineNumber: 345,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-xl font-semibold\",\n                                                            children: selectedGroup ? \"Documents in \".concat((_groups_find = groups.find((g)=>g.id === selectedGroup)) === null || _groups_find === void 0 ? void 0 : _groups_find.name) : 'All Documents'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                            lineNumber: 377,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                    variant: \"ghost\",\n                                                                    size: \"sm\",\n                                                                    onClick: fetchDocuments,\n                                                                    disabled: documentsLoading,\n                                                                    children: documentsLoading ? \"Loading...\" : \"Refresh\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                    lineNumber: 383,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                    className: \"bg-purple-600 hover:bg-purple-700\",\n                                                                    onClick: ()=>setUploadModalOpen(true),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                            className: \"h-4 w-4 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                            lineNumber: 395,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \" Upload Document\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                    lineNumber: 391,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                            lineNumber: 382,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 376,\n                                                    columnNumber: 19\n                                                }, this),\n                                                documentsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4 p-4 bg-red-50 border border-red-200 rounded-lg text-red-700\",\n                                                    children: documentsError\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 400,\n                                                    columnNumber: 21\n                                                }, this),\n                                                documentsLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                    children: [\n                                                        1,\n                                                        2,\n                                                        3\n                                                    ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"border rounded-lg p-4 animate-pulse \".concat(theme === \"light\" ? \"border-gray-200 bg-gray-50\" : \"border-neutral-700 bg-neutral-800\"),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-4 bg-gray-300 rounded mb-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                    lineNumber: 413,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-3 bg-gray-200 rounded mb-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                    lineNumber: 414,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-3 bg-gray-200 rounded w-2/3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                    lineNumber: 415,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, i, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                            lineNumber: 407,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 405,\n                                                    columnNumber: 21\n                                                }, this) : documents.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                    children: documents.map((document)=>{\n                                                        var _groups_find;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            onClick: ()=>handleDocumentClick(document),\n                                                            className: \"border rounded-lg p-4 transition-all duration-300 hover:shadow-lg \".concat(document.processing_status === 'completed' ? 'cursor-pointer hover:border-purple-500 hover:shadow-[0_0_15px_rgba(168,85,247,0.3)]' : 'cursor-not-allowed opacity-60', \" \").concat(theme === \"light\" ? \"border-black bg-white\" : \"border-neutral-800\"),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-start justify-between mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                    className: \"h-5 w-5 text-purple-500\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                                    lineNumber: 435,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-medium text-sm truncate\",\n                                                                                    children: document.title\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                                    lineNumber: 436,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                            lineNumber: 434,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2\",\n                                                                            children: [\n                                                                                document.group && !selectedGroup && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs px-2 py-0.5 rounded-full bg-purple-100 text-purple-800\",\n                                                                                    children: ((_groups_find = groups.find((g)=>g.id === Number(document.group))) === null || _groups_find === void 0 ? void 0 : _groups_find.name) || 'Group'\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                                    lineNumber: 440,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                document.processing_status === 'completed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                    className: \"h-4 w-4 text-green-500\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                                    lineNumber: 445,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                document.processing_status === 'processing' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                    className: \"h-4 w-4 text-yellow-500 animate-spin\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                                    lineNumber: 448,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                document.processing_status === 'failed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                                    className: \"h-4 w-4 text-red-500\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                                    lineNumber: 451,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                            lineNumber: 438,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                    lineNumber: 433,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs mb-2 \".concat(theme === \"light\" ? \"text-gray-600\" : \"text-gray-400\"),\n                                                                    children: [\n                                                                        \"Uploaded: \",\n                                                                        new Date(document.uploaded_at).toLocaleDateString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                    lineNumber: 455,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs px-2 py-1 rounded-full \".concat(document.processing_status === 'completed' ? 'bg-green-100 text-green-800' : document.processing_status === 'processing' ? 'bg-yellow-100 text-yellow-800' : document.processing_status === 'failed' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'),\n                                                                            children: document.processing_status.charAt(0).toUpperCase() + document.processing_status.slice(1)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                            lineNumber: 459,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        document.processing_status === 'completed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs \".concat(theme === \"light\" ? \"text-gray-500\" : \"text-gray-400\"),\n                                                                            children: \"Click to learn\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                            lineNumber: 471,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                    lineNumber: 458,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, document.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                            lineNumber: 422,\n                                                            columnNumber: 25\n                                                        }, this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 420,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border rounded-lg p-8 text-center \".concat(theme === \"light\" ? \"border-gray-200 bg-gray-50\" : \"border-neutral-700 bg-neutral-800\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            className: \"h-12 w-12 mx-auto mb-4 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                            lineNumber: 483,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-medium mb-2\",\n                                                            children: [\n                                                                \"No documents \",\n                                                                selectedGroup ? 'in this space' : 'yet'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                            lineNumber: 484,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm mb-4 \".concat(theme === \"light\" ? \"text-gray-600\" : \"text-gray-400\"),\n                                                            children: \"Upload your first document to get started with learning\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                            lineNumber: 485,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            onClick: ()=>setUploadModalOpen(true),\n                                                            className: \"bg-purple-600 hover:bg-purple-700\",\n                                                            children: \"Upload Document\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                            lineNumber: 488,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 480,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                    lineNumber: 344,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                lineNumber: 343,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-semibold\",\n                                                children: \"Continue learning\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 503,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"link\",\n                                                className: \"text-neutral-400\",\n                                                children: \"View all\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 504,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 502,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border rounded-lg overflow-hidden hover:border-purple-500 transition-all duration-300 \".concat(theme === \"light\" ? \"border-black bg-white\" : \"border-neutral-800\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-32 bg-purple-900 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-4xl font-bold text-white\",\n                                                        children: \"MAP\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                        lineNumber: 516,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 515,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 510,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border rounded-lg overflow-hidden hover:border-purple-500 transition-all duration-300 \".concat(theme === \"light\" ? \"border-black bg-white\" : \"border-neutral-800\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-32 \".concat(theme === \"light\" ? \"bg-gray-100\" : \"bg-neutral-900\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 525,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 520,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border rounded-lg overflow-hidden hover:border-purple-500 transition-all duration-300 \".concat(theme === \"light\" ? \"border-black bg-white\" : \"border-neutral-800\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-32 \".concat(theme === \"light\" ? \"bg-gray-100\" : \"bg-neutral-900\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 533,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 528,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 509,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                lineNumber: 501,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                        lineNumber: 225,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                lineNumber: 178,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_upload_modal__WEBPACK_IMPORTED_MODULE_8__.UploadModal, {\n                isOpen: uploadModalOpen,\n                setIsOpen: setUploadModalOpen,\n                onUploadSuccess: fetchDocuments,\n                groupId: selectedGroup,\n                groups: groups\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                lineNumber: 541,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_paste_modal__WEBPACK_IMPORTED_MODULE_9__.PasteModal, {\n                isOpen: pasteModalOpen,\n                setIsOpen: setPasteModalOpen\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                lineNumber: 548,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_record_modal__WEBPACK_IMPORTED_MODULE_10__.RecordModal, {\n                isOpen: recordModalOpen,\n                setIsOpen: setRecordModalOpen\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                lineNumber: 549,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_create_space_dialog__WEBPACK_IMPORTED_MODULE_7__.CreateSpaceDialog, {\n                open: createSpaceOpen,\n                setOpen: setCreateSpaceOpen,\n                onCreated: ()=>{\n                    // Refresh groups after creation\n                    (async ()=>{\n                        try {\n                            const groupsData = await _lib_api__WEBPACK_IMPORTED_MODULE_12__.documentApi.getGroups();\n                            setGroups(Array.isArray(groupsData) ? groupsData : groupsData.results || []);\n                        } catch (error) {\n                            setGroups([]);\n                        }\n                    })();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                lineNumber: 552,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n        lineNumber: 167,\n        columnNumber: 5\n    }, this);\n}\n_s(CogniUI, \"qKnYNDzkefjcMbBSEb3k81a2SnU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter,\n        _components_theme_provider__WEBPACK_IMPORTED_MODULE_6__.useTheme\n    ];\n});\n_c = CogniUI;\nvar _c;\n$RefreshReg$(_c, \"CogniUI\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/cogni-ui.tsx\n"));

/***/ })

});