'use client'

import { useEffect, useRef, useCallback } from 'react'
import { useRouter } from 'next/navigation'

interface UseDocumentTimeProps {
  documentId?: number
  enabled?: boolean
  isProcessingComplete?: boolean  // New prop to control when tracking starts
  onQuizStart?: () => void
  onQuizEnd?: () => void
}

export function useDocumentTime({
  documentId,
  enabled = true,
  isProcessingComplete = false,  // Only start tracking after processing is complete
  onQuizStart,
  onQuizEnd
}: UseDocumentTimeProps = {}) {
  const sessionIdRef = useRef<number | null>(null)
  const isActiveRef = useRef(false)
  const isPausedRef = useRef(false)
  const currentDocumentRef = useRef<number | undefined>(documentId)
  const sessionStartTimeRef = useRef<Date | null>(null)
  const router = useRouter()

  // Get the correct API base URL
  const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api'

  const getAuthHeaders = () => {
    const token = localStorage.getItem('token')
    return {
      'Content-Type': 'application/json',
      'Authorization': `Token ${token}`,
    }
  }

  const startSession = useCallback(async () => {
    if (!enabled || !documentId || !isProcessingComplete || isActiveRef.current) return

    try {
      const token = localStorage.getItem('token')
      if (!token) return

      const response = await fetch(`${API_BASE_URL}/users/platform-time/start/`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify({ document_id: documentId }),
      })

      if (response.ok) {
        const data = await response.json()
        sessionIdRef.current = data.session_id
        isActiveRef.current = true
        isPausedRef.current = false
        currentDocumentRef.current = documentId
        sessionStartTimeRef.current = new Date() // Record when session started
        console.log('Document time session started:', data.session_id, data.message)
      }
    } catch (error) {
      console.error('Error starting document time session:', error)
    }
  }, [documentId, enabled, isProcessingComplete])

  const pauseSession = useCallback(async (reason: 'quiz' | 'navigation' | 'manual' = 'manual') => {
    if (!documentId || !isActiveRef.current || isPausedRef.current) return

    try {
      const token = localStorage.getItem('token')
      if (!token) return

      const response = await fetch(`${API_BASE_URL}/users/platform-time/pause/`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify({
          document_id: documentId,
          reason: reason
        }),
      })

      if (response.ok) {
        isPausedRef.current = true
        console.log(`Document time session paused: ${reason}`)
      }
    } catch (error) {
      console.error('Error pausing document time session:', error)
    }
  }, [documentId])

  const resumeSession = useCallback(async () => {
    if (!documentId || !isActiveRef.current || !isPausedRef.current) return

    try {
      const token = localStorage.getItem('token')
      if (!token) return

      const response = await fetch(`${API_BASE_URL}/users/platform-time/resume/`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify({ document_id: documentId }),
      })

      if (response.ok) {
        isPausedRef.current = false
        console.log('Document time session resumed')
      }
    } catch (error) {
      console.error('Error resuming document time session:', error)
    }
  }, [documentId])

  const endSession = useCallback(async () => {
    if (!documentId || !isActiveRef.current) return

    try {
      const token = localStorage.getItem('token')
      if (!token) return

      await fetch(`${API_BASE_URL}/users/platform-time/end/`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify({ document_id: documentId }),
      })

      sessionIdRef.current = null
      isActiveRef.current = false
      isPausedRef.current = false
      sessionStartTimeRef.current = null
      console.log('Document time session ended')
    } catch (error) {
      console.error('Error ending document time session:', error)
    }
  }, [documentId])

  // Quiz control functions
  const startQuiz = useCallback(() => {
    pauseSession('quiz')
    onQuizStart?.()
  }, [pauseSession, onQuizStart])

  const endQuiz = useCallback(() => {
    resumeSession()
    onQuizEnd?.()
  }, [resumeSession, onQuizEnd])

  useEffect(() => {
    if (!enabled || !documentId || !isProcessingComplete) return

    // If document changed, end previous session and start new one
    if (currentDocumentRef.current !== documentId) {
      if (isActiveRef.current) {
        endSession()
      }
      currentDocumentRef.current = documentId
    }

    // Start session only when processing is complete (learning phase begins)
    startSession()

    // Handle page unload - end session when user leaves
    const handleBeforeUnload = () => {
      endSession()
    }

    // Handle navigation away from process page - end session
    const handleRouteChange = () => {
      if (window.location.pathname !== '/process') {
        endSession()
      }
    }

    window.addEventListener('beforeunload', handleBeforeUnload)
    window.addEventListener('popstate', handleRouteChange)

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
      window.removeEventListener('popstate', handleRouteChange)
      endSession()
    }
  }, [documentId, enabled, isProcessingComplete, startSession, endSession])

  return {
    sessionId: sessionIdRef.current,
    isActive: isActiveRef.current,
    isPaused: isPausedRef.current,
    sessionStartTime: sessionStartTimeRef.current,
    startQuiz,
    endQuiz,
    pauseSession,
    resumeSession,
    endSession,
  }
}
