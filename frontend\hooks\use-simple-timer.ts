'use client'

import { useEffect, useRef, useCallback } from 'react'
import { useRouter } from 'next/navigation'

interface UseSimpleTimerProps {
  fileName?: string
  enabled?: boolean
  isProcessingComplete?: boolean
}

export function useSimpleTimer({
  fileName,
  enabled = true,
  isProcessingComplete = false
}: UseSimpleTimerProps = {}) {
  const sessionStartRef = useRef<Date | null>(null)
  const isTrackingRef = useRef(false)
  const router = useRouter()

  // Get the correct API base URL
  const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api'

  const getAuthHeaders = () => {
    const token = localStorage.getItem('token')
    return {
      'Content-Type': 'application/json',
      'Authorization': `Token ${token}`,
    }
  }

  const startTimer = useCallback(async () => {
    if (!enabled || !fileName || !isProcessingComplete || isTrackingRef.current) return

    try {
      const token = localStorage.getItem('token')
      if (!token) return

      console.log('Starting timer for file:', fileName)

      const response = await fetch(`${API_BASE_URL}/users/timer/start/`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify({ file_name: fileName }),
      })

      if (response.ok) {
        const data = await response.json()
        sessionStartRef.current = new Date()
        isTrackingRef.current = true
        console.log('Timer started successfully:', data.message)
      } else {
        console.error('Failed to start timer:', response.status, response.statusText)
      }
    } catch (error) {
      console.error('Error starting timer:', error)
    }
  }, [fileName, enabled, isProcessingComplete])

  const stopTimer = useCallback(async () => {
    if (!fileName || !isTrackingRef.current || !sessionStartRef.current) return

    try {
      const token = localStorage.getItem('token')
      if (!token) return

      // Calculate session time in seconds
      const sessionEndTime = new Date()
      const sessionTimeSeconds = Math.floor((sessionEndTime.getTime() - sessionStartRef.current.getTime()) / 1000)

      console.log('Stopping timer for file:', fileName, 'Session time:', sessionTimeSeconds, 'seconds')

      const response = await fetch(`${API_BASE_URL}/users/timer/stop/`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify({ 
          file_name: fileName,
          session_time_seconds: sessionTimeSeconds
        }),
      })

      if (response.ok) {
        const data = await response.json()
        console.log('Timer stopped successfully:', data.message)
        console.log('Total time:', data.total_time_formatted, 'Sessions:', data.number_of_sessions)
      } else {
        console.error('Failed to stop timer:', response.status, response.statusText)
      }

      // Reset state
      sessionStartRef.current = null
      isTrackingRef.current = false
    } catch (error) {
      console.error('Error stopping timer:', error)
      // Reset state even on error
      sessionStartRef.current = null
      isTrackingRef.current = false
    }
  }, [fileName])

  const incrementQuizCount = useCallback(async () => {
    if (!fileName) return

    try {
      const token = localStorage.getItem('token')
      if (!token) return

      const response = await fetch(`${API_BASE_URL}/users/timer/quiz/`, {
        method: 'POST',
        headers: getAuthHeaders(),
        body: JSON.stringify({ file_name: fileName }),
      })

      if (response.ok) {
        const data = await response.json()
        console.log('Quiz count incremented:', data.number_of_quizzes)
      }
    } catch (error) {
      console.error('Error incrementing quiz count:', error)
    }
  }, [fileName])

  // Start timer when component mounts and conditions are met
  useEffect(() => {
    if (!enabled || !fileName || !isProcessingComplete) return

    startTimer()

    // Handle page unload - stop timer when user leaves
    const handleBeforeUnload = () => {
      stopTimer()
    }

    window.addEventListener('beforeunload', handleBeforeUnload)

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload)
    }
  }, [fileName, enabled, isProcessingComplete, startTimer, stopTimer])

  // Stop timer when component unmounts
  useEffect(() => {
    return () => {
      if (isTrackingRef.current) {
        stopTimer()
      }
    }
  }, [stopTimer])

  return {
    isTracking: isTrackingRef.current,
    sessionStart: sessionStartRef.current,
    stopTimer,
    incrementQuizCount,
  }
}
