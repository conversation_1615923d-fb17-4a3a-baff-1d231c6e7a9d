"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/cogni-ui.tsx":
/*!*********************************!*\
  !*** ./components/cogni-ui.tsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CogniUI: () => (/* binding */ CogniUI)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Crown,CuboidIcon,FileText,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Crown,CuboidIcon,FileText,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Crown,CuboidIcon,FileText,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Crown,CuboidIcon,FileText,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Crown,CuboidIcon,FileText,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Crown,CuboidIcon,FileText,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Crown,CuboidIcon,FileText,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/link.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Crown,CuboidIcon,FileText,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mic.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Crown,CuboidIcon,FileText,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Crown,CuboidIcon,FileText,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cuboid.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Crown,CuboidIcon,FileText,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Crown,CuboidIcon,FileText,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-check-big.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Crown,CuboidIcon,FileText,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=AlertCircle,CheckCircle,Clock,Crown,CuboidIcon,FileText,Link,LogOut,Menu,Mic,Moon,Plus,Sun,Upload!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-alert.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/sidebar */ \"(app-pages-browser)/./components/sidebar.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/theme-provider */ \"(app-pages-browser)/./components/theme-provider.tsx\");\n/* harmony import */ var _components_create_space_dialog__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/create-space-dialog */ \"(app-pages-browser)/./components/create-space-dialog.tsx\");\n/* harmony import */ var _components_upload_modal__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/upload-modal */ \"(app-pages-browser)/./components/upload-modal.tsx\");\n/* harmony import */ var _components_paste_modal__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/paste-modal */ \"(app-pages-browser)/./components/paste-modal.tsx\");\n/* harmony import */ var _components_record_modal__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/record-modal */ \"(app-pages-browser)/./components/record-modal.tsx\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* harmony import */ var _lib_navigation_events__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/navigation-events */ \"(app-pages-browser)/./lib/navigation-events.ts\");\n/* __next_internal_client_entry_do_not_use__ CogniUI auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction CogniUI() {\n    var _groups_find;\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    const { theme, setTheme } = (0,_components_theme_provider__WEBPACK_IMPORTED_MODULE_6__.useTheme)();\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [createSpaceOpen, setCreateSpaceOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [uploadModalOpen, setUploadModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [pasteModalOpen, setPasteModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [recordModalOpen, setRecordModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Mock user state - in a real app, this would come from your auth system\n    const [isLoggedIn, setIsLoggedIn] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    // Documents state\n    const [documents, setDocuments] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [documentsLoading, setDocumentsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [documentsError, setDocumentsError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [groups, setGroups] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedGroup, setSelectedGroup] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleUpgradeClick = ()=>{\n        _lib_navigation_events__WEBPACK_IMPORTED_MODULE_13__.navigationEvents.triggerNavigation();\n        router.push(\"/subscription\");\n    };\n    const handleSignIn = ()=>{\n        _lib_navigation_events__WEBPACK_IMPORTED_MODULE_13__.navigationEvents.triggerNavigation();\n        router.push(\"/auth\");\n    };\n    const handleLogout = ()=>{\n        // Clear localStorage\n        localStorage.removeItem(\"isLoggedIn\");\n        localStorage.removeItem(\"username\");\n        localStorage.removeItem(\"token\");\n        setIsLoggedIn(false);\n        setUsername(\"\");\n        setDocuments([]);\n        _lib_navigation_events__WEBPACK_IMPORTED_MODULE_13__.navigationEvents.triggerNavigation();\n        router.push(\"/\");\n    };\n    // Function to fetch user documents\n    const fetchDocuments = async ()=>{\n        if (!isLoggedIn) return;\n        setDocumentsLoading(true);\n        setDocumentsError(null);\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_12__.documentApi.getDocuments();\n            setDocuments(response.results || response || []);\n        } catch (error) {\n            console.error('Error fetching documents:', error);\n            setDocumentsError('Failed to load documents');\n        } finally{\n            setDocumentsLoading(false);\n        }\n    };\n    // Handle document click - navigate to learning page\n    const handleDocumentClick = (document)=>{\n        if (document.processing_status === 'completed') {\n            _lib_navigation_events__WEBPACK_IMPORTED_MODULE_13__.navigationEvents.triggerNavigation();\n            router.push(\"/process?documentId=\".concat(document.id, \"&type=upload\"));\n        }\n    };\n    // Check auth state on mount\n    react__WEBPACK_IMPORTED_MODULE_1___default().useEffect({\n        \"CogniUI.useEffect\": ()=>{\n            const userLoggedIn = localStorage.getItem(\"isLoggedIn\") === \"true\";\n            const storedUsername = localStorage.getItem(\"username\");\n            if (userLoggedIn && storedUsername) {\n                setIsLoggedIn(true);\n                setUsername(storedUsername);\n            }\n        }\n    }[\"CogniUI.useEffect\"], []);\n    // Fetch documents when user logs in\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CogniUI.useEffect\": ()=>{\n            if (isLoggedIn) {\n                fetchDocuments();\n            }\n        }\n    }[\"CogniUI.useEffect\"], [\n        isLoggedIn\n    ]);\n    // Fetch groups when user logs in\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CogniUI.useEffect\": ()=>{\n            const fetchGroups = {\n                \"CogniUI.useEffect.fetchGroups\": async ()=>{\n                    try {\n                        const groupsData = await _lib_api__WEBPACK_IMPORTED_MODULE_12__.documentApi.getGroups();\n                        // Ensure groups is always an array\n                        setGroups(Array.isArray(groupsData) ? groupsData : groupsData.results || []);\n                    } catch (error) {\n                        console.error(\"Failed to fetch groups:\", error);\n                        setGroups([]);\n                    }\n                }\n            }[\"CogniUI.useEffect.fetchGroups\"];\n            if (isLoggedIn) {\n                fetchGroups();\n            }\n        }\n    }[\"CogniUI.useEffect\"], [\n        isLoggedIn\n    ]);\n    // Fetch documents when user logs in or selectedGroup changes\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CogniUI.useEffect\": ()=>{\n            const fetchDocumentsByGroup = {\n                \"CogniUI.useEffect.fetchDocumentsByGroup\": async ()=>{\n                    setDocumentsLoading(true);\n                    setDocumentsError(null);\n                    try {\n                        const response = await _lib_api__WEBPACK_IMPORTED_MODULE_12__.documentApi.getDocuments(selectedGroup || undefined);\n                        setDocuments(response.results || response || []);\n                    } catch (error) {\n                        console.error('Error fetching documents:', error);\n                        setDocumentsError('Failed to load documents');\n                    } finally{\n                        setDocumentsLoading(false);\n                    }\n                }\n            }[\"CogniUI.useEffect.fetchDocumentsByGroup\"];\n            if (isLoggedIn) {\n                fetchDocumentsByGroup();\n            }\n        }\n    }[\"CogniUI.useEffect\"], [\n        isLoggedIn,\n        selectedGroup\n    ]);\n    const handleCreateGroup = ()=>{\n        setCreateSpaceOpen(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-background text-foreground overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sidebar__WEBPACK_IMPORTED_MODULE_3__.Sidebar, {\n                isOpen: sidebarOpen,\n                setIsOpen: setSidebarOpen,\n                isLoggedIn: isLoggedIn,\n                username: username,\n                onLogout: handleLogout\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                lineNumber: 170,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                className: \"h-10 w-10 rounded-full\",\n                                onClick: ()=>setSidebarOpen(true),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                lineNumber: 181,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    isLoggedIn ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenu, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenuTrigger, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"outline\",\n                                                    className: \"border-2 border-gray-300 hover:border-gray-500 bg-white hover:bg-gray-50 text-gray-900 dark:bg-gray-900 dark:text-gray-100 dark:border-gray-600 dark:hover:border-gray-400 dark:hover:bg-gray-800\",\n                                                    children: username\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 189,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenuContent, {\n                                                align: \"end\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_11__.DropdownMenuItem, {\n                                                    onClick: handleLogout,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                            lineNumber: 193,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Log out\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                            lineNumber: 194,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 191,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 187,\n                                        columnNumber: 15\n                                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        className: \"bg-purple-600 hover:bg-purple-700\",\n                                        onClick: handleSignIn,\n                                        children: \"Sign In / Register\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        className: \"bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white border-0\",\n                                        size: \"sm\",\n                                        onClick: handleUpgradeClick,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 210,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Upgrade\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        className: \"h-10 w-10 rounded-full\",\n                                        onClick: ()=>setTheme(theme === \"dark\" ? \"light\" : \"dark\"),\n                                        children: theme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 35\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                            lineNumber: 221,\n                                            columnNumber: 65\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 215,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                lineNumber: 184,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                        lineNumber: 180,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"max-w-5xl mx-auto px-4 py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex justify-center mb-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative h-16 w-16\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                        src: \"https://hebbkx1anhila5yf.public.blob.vercel-storage.com/logo..-modified-dNI2SJo4cpnC8nxN30N6PW6EvffdAE.png\",\n                                        alt: \"Cognimosity Logo\",\n                                        fill: true,\n                                        className: \"object-contain\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 229,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                    lineNumber: 228,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                lineNumber: 227,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl font-bold text-center mb-12\",\n                                children: \"Ready to unlock something new today?\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4 mb-16\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setUploadModalOpen(true),\n                                        className: \"border rounded-lg p-6 flex flex-col items-center transition-all duration-300 hover:border-purple-500 hover:shadow-[0_0_15px_rgba(168,85,247,0.3)] group relative \".concat(theme === \"light\" ? \"border-black bg-white\" : \"border-neutral-800\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute top-2 right-2 bg-purple-500 text-xs px-2 py-0.5 rounded-full\",\n                                                children: \"Popular\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4 p-3 rounded-full transition-all duration-300 \".concat(theme === \"light\" ? \"bg-white border border-black\" : \"bg-neutral-900\", \" group-hover:bg-purple-500/20\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                    className: \"h-6 w-6 group-hover:text-purple-500 transition-all duration-300 \".concat(theme === \"light\" ? \"text-black\" : \"\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 252,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 247,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium mb-1\",\n                                                children: \"Upload\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-center \".concat(theme === \"light\" ? \"text-neutral-600\" : \"text-neutral-400\"),\n                                                children: \"PDF, PPT, DOC, TXT, AUDIO\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 257,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setPasteModalOpen(true),\n                                        className: \"border rounded-lg p-6 flex flex-col items-center transition-all duration-300 hover:border-purple-500 hover:shadow-[0_0_15px_rgba(168,85,247,0.3)] group \".concat(theme === \"light\" ? \"border-black bg-white\" : \"border-neutral-800\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4 p-3 rounded-full transition-all duration-300 \".concat(theme === \"light\" ? \"bg-white border border-black\" : \"bg-neutral-900\", \" group-hover:bg-purple-500/20\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                    className: \"h-6 w-6 group-hover:text-purple-500 transition-all duration-300 \".concat(theme === \"light\" ? \"text-black\" : \"\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 273,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 268,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium mb-1\",\n                                                children: \"Paste\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 277,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-center \".concat(theme === \"light\" ? \"text-neutral-600\" : \"text-neutral-400\"),\n                                                children: \"YouTube, Website, Text\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 278,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 262,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setRecordModalOpen(true),\n                                        className: \"border rounded-lg p-6 flex flex-col items-center transition-all duration-300 hover:border-purple-500 hover:shadow-[0_0_15px_rgba(168,85,247,0.3)] group \".concat(theme === \"light\" ? \"border-black bg-white\" : \"border-neutral-800\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"mb-4 p-3 rounded-full transition-all duration-300 \".concat(theme === \"light\" ? \"bg-white border border-black\" : \"bg-neutral-900\", \" group-hover:bg-purple-500/20\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                                    className: \"h-6 w-6 group-hover:text-purple-500 transition-all duration-300 \".concat(theme === \"light\" ? \"text-black\" : \"\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 294,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium mb-1\",\n                                                children: \"Record\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 298,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-center \".concat(theme === \"light\" ? \"text-neutral-600\" : \"text-neutral-400\"),\n                                                children: \"Record Your Lecture\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 299,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 283,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                lineNumber: 239,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-12\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-semibold\",\n                                                children: \"My spaces\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"outline\",\n                                                className: \"gap-2 border-dashed hover:border-purple-500 transition-all duration-300\",\n                                                onClick: handleCreateGroup,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                        className: \"h-4 w-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                        lineNumber: 313,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Add space\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 308,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-2 gap-4\",\n                                        children: groups.length > 0 ? groups.map((group)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border rounded-lg p-4 flex items-center justify-between hover:border-purple-500 transition-all duration-300 cursor-pointer \".concat(selectedGroup === group.id ? 'border-purple-500 bg-purple-50' : theme === \"light\" ? \"border-black bg-white\" : \"border-neutral-800\"),\n                                                onClick: ()=>setSelectedGroup(group.id),\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-3\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"h-5 w-5\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                lineNumber: 328,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: group.name\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                lineNumber: 329,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                        lineNumber: 327,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xs text-gray-400\",\n                                                        children: new Date(group.created_at).toLocaleDateString()\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                        lineNumber: 331,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, group.id, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 320,\n                                                columnNumber: 19\n                                            }, this)) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border rounded-lg p-4 text-center \".concat(theme === \"light\" ? \"border-gray-200 bg-gray-50\" : \"border-neutral-700 bg-neutral-800\"),\n                                            children: \"No spaces yet. Create one to get started!\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 317,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 11\n                            }, this),\n                            isLoggedIn && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-12\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col gap-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: selectedGroup === null ? \"default\" : \"outline\",\n                                                    onClick: ()=>setSelectedGroup(null),\n                                                    className: \"flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                            lineNumber: 352,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"All Documents\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 347,\n                                                    columnNumber: 19\n                                                }, this),\n                                                groups.map((group)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                        variant: selectedGroup === group.id ? \"default\" : \"outline\",\n                                                        onClick: ()=>setSelectedGroup(group.id),\n                                                        className: \"flex items-center gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                lineNumber: 362,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            group.name\n                                                        ]\n                                                    }, group.id, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                        lineNumber: 356,\n                                                        columnNumber: 21\n                                                    }, this)),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                    variant: \"outline\",\n                                                    className: \"gap-2 border-dashed hover:border-purple-500 transition-all duration-300\",\n                                                    onClick: handleCreateGroup,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_22__[\"default\"], {\n                                                            className: \"h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                            lineNumber: 371,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        \"Add space\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 366,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                            lineNumber: 346,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center justify-between mb-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                            className: \"text-xl font-semibold\",\n                                                            children: selectedGroup ? \"Documents in \".concat((_groups_find = groups.find((g)=>g.id === selectedGroup)) === null || _groups_find === void 0 ? void 0 : _groups_find.name) : 'All Documents'\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                            lineNumber: 378,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"flex gap-2\",\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                    variant: \"ghost\",\n                                                                    size: \"sm\",\n                                                                    onClick: fetchDocuments,\n                                                                    disabled: documentsLoading,\n                                                                    children: documentsLoading ? \"Loading...\" : \"Refresh\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                    lineNumber: 384,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                                    className: \"bg-purple-600 hover:bg-purple-700\",\n                                                                    onClick: ()=>setUploadModalOpen(true),\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                            className: \"h-4 w-4 mr-1\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                            lineNumber: 396,\n                                                                            columnNumber: 25\n                                                                        }, this),\n                                                                        \" Upload Document\"\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                    lineNumber: 392,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                            lineNumber: 383,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 19\n                                                }, this),\n                                                documentsError && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mb-4 p-4 bg-red-50 border border-red-200 rounded-lg text-red-700\",\n                                                    children: documentsError\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 401,\n                                                    columnNumber: 21\n                                                }, this),\n                                                documentsLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                    children: [\n                                                        1,\n                                                        2,\n                                                        3\n                                                    ].map((i)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"border rounded-lg p-4 animate-pulse \".concat(theme === \"light\" ? \"border-gray-200 bg-gray-50\" : \"border-neutral-700 bg-neutral-800\"),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-4 bg-gray-300 rounded mb-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                    lineNumber: 414,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-3 bg-gray-200 rounded mb-1\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                    lineNumber: 415,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-3 bg-gray-200 rounded w-2/3\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                    lineNumber: 416,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, i, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                            lineNumber: 408,\n                                                            columnNumber: 25\n                                                        }, this))\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 406,\n                                                    columnNumber: 21\n                                                }, this) : documents.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                    children: documents.map((document)=>{\n                                                        var _groups_find;\n                                                        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            onClick: ()=>handleDocumentClick(document),\n                                                            className: \"border rounded-lg p-4 transition-all duration-300 hover:shadow-lg \".concat(document.processing_status === 'completed' ? 'cursor-pointer hover:border-purple-500 hover:shadow-[0_0_15px_rgba(168,85,247,0.3)]' : 'cursor-not-allowed opacity-60', \" \").concat(theme === \"light\" ? \"border-black bg-white\" : \"border-neutral-800\"),\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-start justify-between mb-2\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2\",\n                                                                            children: [\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                                    className: \"h-5 w-5 text-purple-500\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                                    lineNumber: 436,\n                                                                                    columnNumber: 31\n                                                                                }, this),\n                                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"font-medium text-sm truncate\",\n                                                                                    children: document.title\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                                    lineNumber: 437,\n                                                                                    columnNumber: 31\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                            lineNumber: 435,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                            className: \"flex items-center gap-2\",\n                                                                            children: [\n                                                                                document.group && !selectedGroup && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                                    className: \"text-xs px-2 py-0.5 rounded-full bg-purple-100 text-purple-800\",\n                                                                                    children: ((_groups_find = groups.find((g)=>g.id === Number(document.group))) === null || _groups_find === void 0 ? void 0 : _groups_find.name) || 'Group'\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                                    lineNumber: 441,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                document.processing_status === 'completed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                                    className: \"h-4 w-4 text-green-500\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                                    lineNumber: 446,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                document.processing_status === 'processing' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                                    className: \"h-4 w-4 text-yellow-500 animate-spin\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                                    lineNumber: 449,\n                                                                                    columnNumber: 33\n                                                                                }, this),\n                                                                                document.processing_status === 'failed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                                    className: \"h-4 w-4 text-red-500\"\n                                                                                }, void 0, false, {\n                                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                                    lineNumber: 452,\n                                                                                    columnNumber: 33\n                                                                                }, this)\n                                                                            ]\n                                                                        }, void 0, true, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                            lineNumber: 439,\n                                                                            columnNumber: 29\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                    lineNumber: 434,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-xs mb-2 \".concat(theme === \"light\" ? \"text-gray-600\" : \"text-gray-400\"),\n                                                                    children: [\n                                                                        \"Uploaded: \",\n                                                                        new Date(document.uploaded_at).toLocaleDateString()\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                    lineNumber: 456,\n                                                                    columnNumber: 27\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"flex items-center justify-between\",\n                                                                    children: [\n                                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs px-2 py-1 rounded-full \".concat(document.processing_status === 'completed' ? 'bg-green-100 text-green-800' : document.processing_status === 'processing' ? 'bg-yellow-100 text-yellow-800' : document.processing_status === 'failed' ? 'bg-red-100 text-red-800' : 'bg-gray-100 text-gray-800'),\n                                                                            children: document.processing_status.charAt(0).toUpperCase() + document.processing_status.slice(1)\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                            lineNumber: 460,\n                                                                            columnNumber: 29\n                                                                        }, this),\n                                                                        document.processing_status === 'completed' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                            className: \"text-xs \".concat(theme === \"light\" ? \"text-gray-500\" : \"text-gray-400\"),\n                                                                            children: \"Click to learn\"\n                                                                        }, void 0, false, {\n                                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                            lineNumber: 472,\n                                                                            columnNumber: 31\n                                                                        }, this)\n                                                                    ]\n                                                                }, void 0, true, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                                    lineNumber: 459,\n                                                                    columnNumber: 27\n                                                                }, this)\n                                                            ]\n                                                        }, document.id, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                            lineNumber: 423,\n                                                            columnNumber: 25\n                                                        }, this);\n                                                    })\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 21\n                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"border rounded-lg p-8 text-center \".concat(theme === \"light\" ? \"border-gray-200 bg-gray-50\" : \"border-neutral-700 bg-neutral-800\"),\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_AlertCircle_CheckCircle_Clock_Crown_CuboidIcon_FileText_Link_LogOut_Menu_Mic_Moon_Plus_Sun_Upload_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                            className: \"h-12 w-12 mx-auto mb-4 text-gray-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                            lineNumber: 484,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                            className: \"text-lg font-medium mb-2\",\n                                                            children: [\n                                                                \"No documents \",\n                                                                selectedGroup ? 'in this space' : 'yet'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                            lineNumber: 485,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-sm mb-4 \".concat(theme === \"light\" ? \"text-gray-600\" : \"text-gray-400\"),\n                                                            children: \"Upload your first document to get started with learning\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                            lineNumber: 486,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            onClick: ()=>setUploadModalOpen(true),\n                                                            className: \"bg-purple-600 hover:bg-purple-700\",\n                                                            children: \"Upload Document\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                            lineNumber: 489,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 481,\n                                                    columnNumber: 21\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                            lineNumber: 376,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                    lineNumber: 345,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                lineNumber: 344,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-semibold\",\n                                                children: \"Continue learning\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 504,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                variant: \"link\",\n                                                className: \"text-neutral-400\",\n                                                children: \"View all\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 505,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 503,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border rounded-lg overflow-hidden hover:border-purple-500 transition-all duration-300 \".concat(theme === \"light\" ? \"border-black bg-white\" : \"border-neutral-800\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-32 bg-purple-900 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-4xl font-bold text-white\",\n                                                        children: \"MAP\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                        lineNumber: 517,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 516,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 511,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border rounded-lg overflow-hidden hover:border-purple-500 transition-all duration-300 \".concat(theme === \"light\" ? \"border-black bg-white\" : \"border-neutral-800\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-32 \".concat(theme === \"light\" ? \"bg-gray-100\" : \"bg-neutral-900\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 526,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 521,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"border rounded-lg overflow-hidden hover:border-purple-500 transition-all duration-300 \".concat(theme === \"light\" ? \"border-black bg-white\" : \"border-neutral-800\"),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"h-32 \".concat(theme === \"light\" ? \"bg-gray-100\" : \"bg-neutral-900\")\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                    lineNumber: 534,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                                lineNumber: 529,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                                lineNumber: 502,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                        lineNumber: 226,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                lineNumber: 179,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_upload_modal__WEBPACK_IMPORTED_MODULE_8__.UploadModal, {\n                isOpen: uploadModalOpen,\n                setIsOpen: setUploadModalOpen,\n                onUploadSuccess: fetchDocuments,\n                groupId: selectedGroup,\n                groups: groups\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                lineNumber: 542,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_paste_modal__WEBPACK_IMPORTED_MODULE_9__.PasteModal, {\n                isOpen: pasteModalOpen,\n                setIsOpen: setPasteModalOpen\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                lineNumber: 549,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_record_modal__WEBPACK_IMPORTED_MODULE_10__.RecordModal, {\n                isOpen: recordModalOpen,\n                setIsOpen: setRecordModalOpen\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                lineNumber: 550,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_create_space_dialog__WEBPACK_IMPORTED_MODULE_7__.CreateSpaceDialog, {\n                open: createSpaceOpen,\n                setOpen: setCreateSpaceOpen,\n                onCreated: ()=>{\n                    // Refresh groups after creation\n                    (async ()=>{\n                        try {\n                            const groupsData = await _lib_api__WEBPACK_IMPORTED_MODULE_12__.documentApi.getGroups();\n                            setGroups(Array.isArray(groupsData) ? groupsData : groupsData.results || []);\n                        } catch (error) {\n                            setGroups([]);\n                        }\n                    })();\n                }\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n                lineNumber: 553,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\cogni-ui.tsx\",\n        lineNumber: 168,\n        columnNumber: 5\n    }, this);\n}\n_s(CogniUI, \"qKnYNDzkefjcMbBSEb3k81a2SnU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter,\n        _components_theme_provider__WEBPACK_IMPORTED_MODULE_6__.useTheme\n    ];\n});\n_c = CogniUI;\nvar _c;\n$RefreshReg$(_c, \"CogniUI\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/cogni-ui.tsx\n"));

/***/ })

});