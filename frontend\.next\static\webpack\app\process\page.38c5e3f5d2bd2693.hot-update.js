"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/process/page",{

/***/ "(app-pages-browser)/./app/process/page.tsx":
/*!******************************!*\
  !*** ./app/process/page.tsx ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProcessPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/tabs */ \"(app-pages-browser)/./components/ui/tabs.tsx\");\n/* harmony import */ var _components_ui_resizable__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/resizable */ \"(app-pages-browser)/./components/ui/resizable.tsx\");\n/* harmony import */ var _barrel_optimize_names_BarChart4_BookOpen_FileLineChartIcon_FileText_FlashlightIcon_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_23__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart4,BookOpen,FileLineChartIcon,FileText,FlashlightIcon,MessageSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/message-square.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart4_BookOpen_FileLineChartIcon_FileText_FlashlightIcon_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_24__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart4,BookOpen,FileLineChartIcon,FileText,FlashlightIcon,MessageSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-chart-line.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart4_BookOpen_FileLineChartIcon_FileText_FlashlightIcon_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_25__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart4,BookOpen,FileLineChartIcon,FileText,FlashlightIcon,MessageSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart4_BookOpen_FileLineChartIcon_FileText_FlashlightIcon_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_26__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart4,BookOpen,FileLineChartIcon,FileText,FlashlightIcon,MessageSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/flashlight.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart4_BookOpen_FileLineChartIcon_FileText_FlashlightIcon_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_27__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart4,BookOpen,FileLineChartIcon,FileText,FlashlightIcon,MessageSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BarChart4_BookOpen_FileLineChartIcon_FileText_FlashlightIcon_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_28__ = __webpack_require__(/*! __barrel_optimize__?names=BarChart4,BookOpen,FileLineChartIcon,FileText,FlashlightIcon,MessageSquare!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chart-column-increasing.js\");\n/* harmony import */ var _components_upload_content__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/upload-content */ \"(app-pages-browser)/./components/upload-content.tsx\");\n/* harmony import */ var _components_paste_content__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/paste-content */ \"(app-pages-browser)/./components/paste-content.tsx\");\n/* harmony import */ var _components_record_content__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/record-content */ \"(app-pages-browser)/./components/record-content.tsx\");\n/* harmony import */ var _components_chat_interface__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/chat-interface */ \"(app-pages-browser)/./components/chat-interface.tsx\");\n/* harmony import */ var _components_performance_dashboard__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/performance-dashboard */ \"(app-pages-browser)/./components/performance-dashboard.tsx\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_22__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_layout_with_sidebar__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/layout-with-sidebar */ \"(app-pages-browser)/./components/layout-with-sidebar.tsx\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/theme-provider */ \"(app-pages-browser)/./components/theme-provider.tsx\");\n/* harmony import */ var _components_blueprint_interface__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/blueprint-interface */ \"(app-pages-browser)/./components/blueprint-interface.tsx\");\n/* harmony import */ var _components_flowchart_interface__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/flowchart-interface */ \"(app-pages-browser)/./components/flowchart-interface.tsx\");\n/* harmony import */ var _components_flashcards_interface__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/components/flashcards-interface */ \"(app-pages-browser)/./components/flashcards-interface.tsx\");\n/* harmony import */ var _components_summary_interface__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @/components/summary-interface */ \"(app-pages-browser)/./components/summary-interface.tsx\");\n/* harmony import */ var _components_chapters_interface__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @/components/chapters-interface */ \"(app-pages-browser)/./components/chapters-interface.tsx\");\n/* harmony import */ var _components_loading_overlay__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @/components/loading-overlay */ \"(app-pages-browser)/./components/loading-overlay.tsx\");\n/* harmony import */ var _components_file_preview__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @/components/file-preview */ \"(app-pages-browser)/./components/file-preview.tsx\");\n/* harmony import */ var _components_session_time_display__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @/components/session-time-display */ \"(app-pages-browser)/./components/session-time-display.tsx\");\n/* harmony import */ var _hooks_use_document_time__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! @/hooks/use-document-time */ \"(app-pages-browser)/./hooks/use-document-time.ts\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! @/lib/api */ \"(app-pages-browser)/./lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction ProcessPage() {\n    _s();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const type = searchParams.get(\"type\") || \"upload\";\n    const [activeTab, setActiveTab] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"chat\");\n    const [showFullScreen, setShowFullScreen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [uploadedData, setUploadedData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { theme } = (0,_components_theme_provider__WEBPACK_IMPORTED_MODULE_11__.useTheme)();\n    const tabsListRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // State preservation\n    const [chatState, setChatState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [summaryState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [chapterState] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isProcessing, setIsProcessing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [documentDetails, setDocumentDetails] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProcessPage.useEffect\": ()=>{\n            document.body.classList.add(\"page-transition\");\n            // Check if we have a document ID (either from upload or clicking existing document)\n            const documentId = searchParams.get(\"documentId\");\n            if (documentId) {\n                const docId = parseInt(documentId);\n                // If it's a new upload, show processing\n                if (type === \"upload\") {\n                    setIsProcessing(true);\n                    // Check document processing status\n                    checkDocumentStatus(docId);\n                }\n                // Always load document details for preview\n                loadDocumentDetails(docId);\n            }\n            const uploadedFilesStr = localStorage.getItem(\"uploadedFiles\");\n            if (uploadedFilesStr && type === \"upload\") {\n                try {\n                    const uploadedFiles = JSON.parse(uploadedFilesStr);\n                    if (uploadedFiles.length > 0) {\n                        var _file_name_split_pop;\n                        const file = uploadedFiles[0];\n                        const storedDocumentId = localStorage.getItem(\"currentDocumentId\");\n                        setUploadedData({\n                            type: \"file\",\n                            name: file.name,\n                            size: file.size,\n                            fileType: file.type || ((_file_name_split_pop = file.name.split('.').pop()) === null || _file_name_split_pop === void 0 ? void 0 : _file_name_split_pop.toLowerCase()),\n                            documentId: file.documentId || (storedDocumentId ? parseInt(storedDocumentId) : undefined)\n                        });\n                        setShowFullScreen(false);\n                    }\n                } catch (error) {\n                    console.error(\"Error parsing uploaded files:\", error);\n                }\n            } else if (type === \"paste\") {\n                const pastedContentStr = localStorage.getItem(\"pastedContent\");\n                if (pastedContentStr) {\n                    try {\n                        const pastedContent = JSON.parse(pastedContentStr);\n                        setUploadedData({\n                            type: \"text\",\n                            content: pastedContent.content || pastedContent.url || \"Pasted content\"\n                        });\n                        setShowFullScreen(false);\n                    } catch (error) {\n                        console.error(\"Error parsing pasted content:\", error);\n                    }\n                }\n            } else if (type === \"record\") {\n                const recordedAudioStr = localStorage.getItem(\"recordedAudio\");\n                if (recordedAudioStr) {\n                    try {\n                        const recordedAudio = JSON.parse(recordedAudioStr);\n                        setUploadedData({\n                            type: \"audio\",\n                            name: \"Recording-\" + new Date().toISOString().split(\"T\")[0] + \".wav\",\n                            duration: recordedAudio.duration || \"00:00\"\n                        });\n                        setShowFullScreen(false);\n                    } catch (error) {\n                        console.error(\"Error parsing recorded audio:\", error);\n                    }\n                }\n            }\n            return ({\n                \"ProcessPage.useEffect\": ()=>{\n                    document.body.classList.remove(\"page-transition\");\n                }\n            })[\"ProcessPage.useEffect\"];\n        }\n    }[\"ProcessPage.useEffect\"], [\n        type,\n        searchParams\n    ]);\n    const loadDocumentDetails = async (documentId)=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_21__.documentApi.getDocumentDetails(documentId);\n            setDocumentDetails(response);\n            // Create file preview data from document details\n            if (response && response.file) {\n                var _response_title_split_pop;\n                const fileExtension = (_response_title_split_pop = response.title.split('.').pop()) === null || _response_title_split_pop === void 0 ? void 0 : _response_title_split_pop.toLowerCase();\n                const fileData = {\n                    type: \"file\",\n                    name: response.title,\n                    size: \"Unknown\",\n                    fileType: fileExtension || 'unknown',\n                    documentId: documentId // Store document ID for FilePreview component\n                };\n                setUploadedData(fileData);\n                setShowFullScreen(false);\n            }\n        } catch (error) {\n            console.error('Error loading document details:', error);\n        }\n    };\n    const checkDocumentStatus = async (documentId)=>{\n        try {\n            const response = await _lib_api__WEBPACK_IMPORTED_MODULE_21__.documentApi.getDocumentStatus(documentId);\n            if (response.processing_status === 'completed') {\n                setIsProcessing(false);\n            } else if (response.processing_status === 'failed') {\n                setIsProcessing(false);\n            // Could show an error message here\n            } else {\n                // Still processing, check again in 2 seconds\n                setTimeout(()=>checkDocumentStatus(documentId), 2000);\n            }\n        } catch (error) {\n            console.error('Error checking document status:', error);\n            setIsProcessing(false);\n        }\n    };\n    const renderInputComponent = ()=>{\n        // Check if we have a document ID but no uploaded data yet\n        const documentId = searchParams.get(\"documentId\");\n        if (documentId && !uploadedData && !isProcessing) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 h-full flex flex-col items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"rounded-lg p-8 text-center \".concat(theme === \"light\" ? \"bg-white border border-black\" : \"bg-neutral-800\"),\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500 mx-auto mb-4\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg mb-2\",\n                            children: \"Loading document...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-sm \".concat(theme === \"light\" ? \"text-neutral-600\" : \"text-neutral-500\"),\n                            children: \"Please wait while we load your document for preview.\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                            lineNumber: 189,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                lineNumber: 185,\n                columnNumber: 9\n            }, this);\n        }\n        if (uploadedData) {\n            if (uploadedData.type === \"file\") {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 h-full flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold mb-4\",\n                            children: uploadedData.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-hidden\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_file_preview__WEBPACK_IMPORTED_MODULE_18__.FilePreview, {\n                                documentId: uploadedData.documentId,\n                                fileName: uploadedData.name,\n                                fileType: uploadedData.fileType,\n                                className: \"h-full\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                lineNumber: 203,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 200,\n                    columnNumber: 11\n                }, this);\n            } else if (uploadedData.type === \"text\") {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4 h-full flex flex-col\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold mb-4\",\n                            children: \"Pasted Content\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                            lineNumber: 215,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1 overflow-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"rounded-lg p-4 \".concat(theme === \"light\" ? \"bg-white border border-black\" : \"bg-neutral-800\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: uploadedData.content\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                    lineNumber: 218,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 214,\n                    columnNumber: 11\n                }, this);\n            } else if (uploadedData.type === \"audio\") {\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-xl font-semibold mb-4\",\n                            children: uploadedData.name\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                            lineNumber: 226,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"rounded-lg p-4 flex flex-col items-center \".concat(theme === \"light\" ? \"bg-white border border-black\" : \"bg-neutral-800\"),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full h-24 rounded-md mb-4 \".concat(theme === \"light\" ? \"bg-gray-200\" : \"bg-neutral-700\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"audio\", {\n                                    controls: true,\n                                    className: \"w-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"source\", {\n                                        src: \"#\",\n                                        type: \"audio/wav\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                    lineNumber: 231,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-sm mt-2 \".concat(theme === \"light\" ? \"text-neutral-600\" : \"text-neutral-500\"),\n                                    children: [\n                                        \"Duration: \",\n                                        uploadedData.duration\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                            lineNumber: 227,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 225,\n                    columnNumber: 11\n                }, this);\n            }\n        }\n        switch(type){\n            case \"upload\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_upload_content__WEBPACK_IMPORTED_MODULE_5__.UploadContent, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 244,\n                    columnNumber: 29\n                }, this);\n            case \"paste\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_paste_content__WEBPACK_IMPORTED_MODULE_6__.PasteContent, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 245,\n                    columnNumber: 28\n                }, this);\n            case \"record\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_record_content__WEBPACK_IMPORTED_MODULE_7__.RecordContent, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 246,\n                    columnNumber: 29\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_upload_content__WEBPACK_IMPORTED_MODULE_5__.UploadContent, {}, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 247,\n                    columnNumber: 23\n                }, this);\n        }\n    };\n    const renderOutputComponent = ()=>{\n        // Try to get document ID from URL params first, then from localStorage\n        let documentId = searchParams.get(\"documentId\") ? parseInt(searchParams.get(\"documentId\")) : undefined;\n        if (!documentId) {\n            const storedDocumentId = localStorage.getItem(\"currentDocumentId\");\n            if (storedDocumentId) {\n                documentId = parseInt(storedDocumentId);\n            }\n        }\n        switch(activeTab){\n            case \"chat\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_interface__WEBPACK_IMPORTED_MODULE_8__.ChatInterface, {\n                    state: chatState,\n                    setState: setChatState,\n                    documentId: documentId\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 264,\n                    columnNumber: 16\n                }, this);\n            case \"summary\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_summary_interface__WEBPACK_IMPORTED_MODULE_15__.SummaryInterface, {\n                    documentId: documentId\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 266,\n                    columnNumber: 16\n                }, this);\n            case \"flowchart\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_flowchart_interface__WEBPACK_IMPORTED_MODULE_13__.FlowchartInterface, {\n                    documentId: documentId\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 268,\n                    columnNumber: 16\n                }, this);\n            case \"flashcards\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_flashcards_interface__WEBPACK_IMPORTED_MODULE_14__.FlashcardsInterface, {\n                    documentId: documentId\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 270,\n                    columnNumber: 16\n                }, this);\n            case \"chapters\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chapters_interface__WEBPACK_IMPORTED_MODULE_16__.ChaptersInterface, {\n                    documentId: documentId\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 272,\n                    columnNumber: 16\n                }, this);\n            case \"transcript\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_performance_dashboard__WEBPACK_IMPORTED_MODULE_9__.PerformanceDashboard, {\n                    documentId: documentId\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 274,\n                    columnNumber: 16\n                }, this);\n            case \"blueprint\":\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_blueprint_interface__WEBPACK_IMPORTED_MODULE_12__.BlueprintInterface, {\n                    documentId: documentId\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 276,\n                    columnNumber: 16\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_chat_interface__WEBPACK_IMPORTED_MODULE_8__.ChatInterface, {\n                    state: chatState,\n                    setState: setChatState,\n                    documentId: documentId\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 278,\n                    columnNumber: 16\n                }, this);\n        }\n    };\n    // Enhanced document time tracking\n    const documentId = searchParams.get(\"documentId\") ? parseInt(searchParams.get(\"documentId\")) : undefined;\n    const { isActive, isPaused, sessionStartTime, endSession } = (0,_hooks_use_document_time__WEBPACK_IMPORTED_MODULE_20__.useDocumentTime)({\n        documentId: documentId,\n        enabled: !!documentId,\n        isProcessingComplete: !isProcessing\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_layout_with_sidebar__WEBPACK_IMPORTED_MODULE_10__.LayoutWithSidebar, {\n        showQuizButton: true,\n        showUpgradeButton: true,\n        documentId: documentId,\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_loading_overlay__WEBPACK_IMPORTED_MODULE_17__.LoadingOverlay, {\n                isVisible: isProcessing,\n                message: \"Processing your document. This may take a few moments...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                lineNumber: 299,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_22__.motion.div, {\n                className: \"h-[calc(100vh-65px)] overflow-hidden bg-background text-foreground flex flex-col\",\n                initial: {\n                    opacity: 0\n                },\n                animate: {\n                    opacity: 1\n                },\n                transition: {\n                    duration: 0.3\n                },\n                children: showFullScreen ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_22__.motion.div, {\n                    className: \"flex-1 p-4\",\n                    children: renderInputComponent()\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 308,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_resizable__WEBPACK_IMPORTED_MODULE_4__.ResizablePanelGroup, {\n                    direction: \"horizontal\",\n                    className: \"flex-1 overflow-hidden\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_resizable__WEBPACK_IMPORTED_MODULE_4__.ResizablePanel, {\n                            defaultSize: 40,\n                            minSize: 30,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full overflow-auto\",\n                                children: renderInputComponent()\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_resizable__WEBPACK_IMPORTED_MODULE_4__.ResizableHandle, {\n                            withHandle: true\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                            lineNumber: 314,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_resizable__WEBPACK_IMPORTED_MODULE_4__.ResizablePanel, {\n                            defaultSize: 60,\n                            minSize: 30,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-full flex flex-col\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.Tabs, {\n                                    defaultValue: \"chat\",\n                                    onValueChange: setActiveTab,\n                                    value: activeTab,\n                                    className: \"w-full h-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"border-b border-border sticky top-0 bg-background z-10\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"px-4 overflow-x-auto scrollbar-hide flex items-center justify-between\",\n                                                ref: tabsListRef,\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsList, {\n                                                        className: \"h-12 w-max\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                                                value: \"chat\",\n                                                                className: \"gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart4_BookOpen_FileLineChartIcon_FileText_FlashlightIcon_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_23__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                                        lineNumber: 322,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Chat\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                                lineNumber: 321,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                                                value: \"blueprint\",\n                                                                className: \"gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart4_BookOpen_FileLineChartIcon_FileText_FlashlightIcon_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                                        lineNumber: 325,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Blueprint\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                                lineNumber: 324,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                                                value: \"summary\",\n                                                                className: \"gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart4_BookOpen_FileLineChartIcon_FileText_FlashlightIcon_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_25__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                                        lineNumber: 328,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Summary\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                                lineNumber: 327,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                                                value: \"flowchart\",\n                                                                className: \"gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart4_BookOpen_FileLineChartIcon_FileText_FlashlightIcon_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_24__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                                        lineNumber: 331,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Flowchart\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                                lineNumber: 330,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                                                value: \"flashcards\",\n                                                                className: \"gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart4_BookOpen_FileLineChartIcon_FileText_FlashlightIcon_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_26__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                                        lineNumber: 334,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Flashcards\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                                lineNumber: 333,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                                                value: \"chapters\",\n                                                                className: \"gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart4_BookOpen_FileLineChartIcon_FileText_FlashlightIcon_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_27__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                                        lineNumber: 337,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Chapters\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                                lineNumber: 336,\n                                                                columnNumber: 25\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsTrigger, {\n                                                                value: \"transcript\",\n                                                                className: \"gap-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BarChart4_BookOpen_FileLineChartIcon_FileText_FlashlightIcon_MessageSquare_lucide_react__WEBPACK_IMPORTED_MODULE_28__[\"default\"], {\n                                                                        className: \"h-4 w-4\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                                        lineNumber: 340,\n                                                                        columnNumber: 27\n                                                                    }, this),\n                                                                    \"Performance\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                                lineNumber: 339,\n                                                                columnNumber: 25\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 ml-4\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_session_time_display__WEBPACK_IMPORTED_MODULE_19__.SessionTimeDisplay, {\n                                                            isActive: isActive,\n                                                            isPaused: isPaused,\n                                                            sessionStartTime: sessionStartTime,\n                                                            className: \"text-muted-foreground\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                            lineNumber: 344,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                        lineNumber: 343,\n                                                        columnNumber: 23\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                            lineNumber: 318,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_tabs__WEBPACK_IMPORTED_MODULE_3__.TabsContent, {\n                                            value: activeTab,\n                                            className: \"flex-1 p-0 m-0 overflow-hidden h-full\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-full overflow-y-auto pb-8 scrollbar-thin scrollbar-thumb-neutral-500 scrollbar-track-transparent hover:scrollbar-thumb-neutral-400\",\n                                                children: renderOutputComponent()\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                                lineNumber: 355,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                    lineNumber: 317,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                                lineNumber: 316,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                            lineNumber: 315,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                    lineNumber: 310,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n                lineNumber: 303,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\app\\\\process\\\\page.tsx\",\n        lineNumber: 294,\n        columnNumber: 5\n    }, this);\n}\n_s(ProcessPage, \"CBtlzEOiN4JSaibdK3nqr0+AIXU=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _components_theme_provider__WEBPACK_IMPORTED_MODULE_11__.useTheme,\n        _hooks_use_document_time__WEBPACK_IMPORTED_MODULE_20__.useDocumentTime\n    ];\n});\n_c = ProcessPage;\nvar _c;\n$RefreshReg$(_c, \"ProcessPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/process/page.tsx\n"));

/***/ })

});