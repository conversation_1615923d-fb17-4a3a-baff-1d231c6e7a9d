#!/usr/bin/env python3
"""
Test script to verify Gemini API connectivity and functionality.
"""

import os
import sys
from dotenv import load_dotenv
import google.generativeai as genai

# Load environment variables
load_dotenv()

def test_gemini_api():
    """Test basic Gemini API connectivity"""
    try:
        # Get API key
        api_key = os.getenv("GOOGLE_API_KEY") or os.getenv("GEMINI_API_KEY")
        
        if not api_key:
            print("❌ No Gemini API key found in environment variables")
            return False
        
        print(f"✓ API key found: {api_key[:10]}...")
        
        # Configure Gemini
        genai.configure(api_key=api_key)
        
        # Test model listing
        print("✓ Gemini configured successfully")
        
        # Test a simple generation
        model = genai.GenerativeModel('gemini-2.0-flash')
        response = model.generate_content("Hello, this is a test. Please respond with 'API working'.")
        
        if response and response.text:
            print(f"✓ Gemini API response: {response.text.strip()}")
            return True
        else:
            print("❌ Gemini API returned empty response")
            return False
            
    except Exception as e:
        print(f"❌ Gemini API test failed: {str(e)}")
        return False

def test_learning_time_analysis():
    """Test learning time analysis with Gemini"""
    try:
        api_key = os.getenv("GOOGLE_API_KEY") or os.getenv("GEMINI_API_KEY")
        genai.configure(api_key=api_key)
        
        # Sample text for analysis
        sample_text = """
        Artificial Intelligence (AI) is a branch of computer science that aims to create 
        intelligent machines that work and react like humans. Some of the activities 
        computers with artificial intelligence are designed for include speech recognition, 
        learning, planning, and problem-solving.
        """
        
        # Create the prompt for learning time analysis
        prompt = f"""
        Analyze the following educational content and predict learning time for students aged 16-22:

        Content: {sample_text}

        Please provide:
        1. Topic difficulty (1-5 scale, where 1=very easy, 5=very difficult)
        2. Content length in words
        3. Concept density (1-5 scale, where 1=low density, 5=high density)
        4. Estimated learning time in seconds
        5. Brief reasoning

        Respond in JSON format:
        {{
            "topic_difficulty": <number>,
            "content_length_words": <number>,
            "concept_density": <number>,
            "estimated_time_seconds": <number>,
            "reasoning": "<explanation>"
        }}
        """
        
        model = genai.GenerativeModel('gemini-2.0-flash')
        response = model.generate_content(prompt)
        
        if response and response.text:
            print(f"✓ Learning time analysis response:")
            print(response.text.strip())
            return True
        else:
            print("❌ Learning time analysis returned empty response")
            return False
            
    except Exception as e:
        print(f"❌ Learning time analysis test failed: {str(e)}")
        return False

def main():
    print("=" * 60)
    print("GEMINI API CONNECTIVITY TEST")
    print("=" * 60)
    
    # Test 1: Basic API connectivity
    print("\n1. Testing basic Gemini API connectivity...")
    basic_test = test_gemini_api()
    
    # Test 2: Learning time analysis
    if basic_test:
        print("\n2. Testing learning time analysis...")
        analysis_test = test_learning_time_analysis()
    else:
        print("\n2. Skipping learning time analysis (basic test failed)")
        analysis_test = False
    
    # Summary
    print("\n" + "=" * 60)
    print("TEST SUMMARY")
    print("=" * 60)
    print(f"Basic API Test: {'✓ PASS' if basic_test else '❌ FAIL'}")
    print(f"Learning Time Analysis: {'✓ PASS' if analysis_test else '❌ FAIL'}")
    
    if basic_test and analysis_test:
        print("\n🎉 Gemini API is working correctly!")
        print("The learning time prediction system should work now.")
    else:
        print("\n❌ Gemini API tests failed.")
        print("Please check your API key and internet connection.")

if __name__ == '__main__':
    main()
