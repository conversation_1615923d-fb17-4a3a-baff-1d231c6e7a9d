#!/usr/bin/env python3
"""
Check available documents via Django API.
"""

import requests

def main():
    auth_token = "25b0bb2b7a8d6307956c7343b4045c0bebf37c40"

    # First, check what user this token belongs to
    user_response = requests.get(
        'http://localhost:8000/api/users/auth/validate-token/',
        headers={'Authorization': f'Token {auth_token}'}
    )

    print(f"User validation status: {user_response.status_code}")
    if user_response.status_code == 200:
        user_data = user_response.json()
        print(f"Token belongs to user: {user_data}")

    # Get documents
    response = requests.get(
        'http://localhost:8000/api/documents/',
        headers={'Authorization': f'Token {auth_token}'}
    )
    
    print(f"Documents API status: {response.status_code}")
    
    if response.status_code == 200:
        data = response.json()
        print(f"Response type: {type(data)}")
        print(f"Response keys: {data.keys() if isinstance(data, dict) else 'Not a dict'}")

        if isinstance(data, dict) and 'results' in data:
            documents = data['results']
        elif isinstance(data, list):
            documents = data
        else:
            print(f"Unexpected response format: {data}")
            return

        print(f"Found {len(documents)} documents")

        for doc in documents[:5]:
            print(f"ID: {doc['id']}, Title: {doc['title']}, Status: {doc.get('processing_status', 'unknown')}")

            # Check embeddings for this document
            embeddings_response = requests.get(
                f'http://localhost:8000/api/documents/{doc["id"]}/embeddings/',
                headers={'Authorization': f'Token {auth_token}'}
            )

            if embeddings_response.status_code == 200:
                embeddings = embeddings_response.json()
                print(f"  -> Embeddings: {len(embeddings)}")
            else:
                print(f"  -> Embeddings error: {embeddings_response.status_code}")
    else:
        print(f"Error: {response.text}")

if __name__ == '__main__':
    main()
