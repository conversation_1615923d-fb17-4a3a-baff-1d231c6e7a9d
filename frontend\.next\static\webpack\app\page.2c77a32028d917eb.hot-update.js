"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./components/sidebar.tsx":
/*!********************************!*\
  !*** ./components/sidebar.tsx ***!
  \********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Sidebar: () => (/* binding */ Sidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Clock,CuboidIcon,LogOut,Moon,Plus,Sun,ThumbsUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Clock,CuboidIcon,LogOut,Moon,Plus,Sun,ThumbsUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Clock,CuboidIcon,LogOut,Moon,Plus,Sun,ThumbsUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Clock,CuboidIcon,LogOut,Moon,Plus,Sun,ThumbsUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Clock,CuboidIcon,LogOut,Moon,Plus,Sun,ThumbsUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/cuboid.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Clock,CuboidIcon,LogOut,Moon,Plus,Sun,ThumbsUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Clock,CuboidIcon,LogOut,Moon,Plus,Sun,ThumbsUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Clock,CuboidIcon,LogOut,Moon,Plus,Sun,ThumbsUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/thumbs-up.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Clock,CuboidIcon,LogOut,Moon,Plus,Sun,ThumbsUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/users.js\");\n/* harmony import */ var _barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=BookOpen,Clock,CuboidIcon,LogOut,Moon,Plus,Sun,ThumbsUp,Users,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/components/AnimatePresence/index.mjs\");\n/* harmony import */ var framer_motion__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! framer-motion */ \"(app-pages-browser)/./node_modules/framer-motion/dist/es/render/components/motion/proxy.mjs\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/theme-provider */ \"(app-pages-browser)/./components/theme-provider.tsx\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* __next_internal_client_entry_do_not_use__ Sidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\nfunction Sidebar(param) {\n    let { isOpen, setIsOpen, isLoggedIn = false, username = \"\", onLogout } = param;\n    _s();\n    const { theme, setTheme } = (0,_components_theme_provider__WEBPACK_IMPORTED_MODULE_3__.useTheme)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter)();\n    // Determine text color based on theme\n    const textColor = theme === \"dark\" ? \"text-neutral-300\" : \"text-neutral-800\";\n    const iconColor = theme === \"dark\" ? \"text-neutral-400\" : \"text-neutral-600\";\n    const goToHome = ()=>{\n        router.push(\"/\");\n        setIsOpen(false);\n    };\n    const handleSignIn = ()=>{\n        router.push(\"/auth\");\n        setIsOpen(false);\n    };\n    const handleLogout = ()=>{\n        if (onLogout) {\n            onLogout();\n        }\n        setIsOpen(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_5__.AnimatePresence, {\n                children: isOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                    initial: {\n                        opacity: 0\n                    },\n                    animate: {\n                        opacity: 0.5\n                    },\n                    exit: {\n                        opacity: 0\n                    },\n                    transition: {\n                        duration: 0.2\n                    },\n                    className: \"fixed inset-0 bg-black z-40 md:hidden\",\n                    onClick: ()=>setIsOpen(false)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                    lineNumber: 48,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(framer_motion__WEBPACK_IMPORTED_MODULE_6__.motion.div, {\n                initial: {\n                    x: \"-100%\"\n                },\n                animate: {\n                    x: isOpen ? 0 : \"-100%\"\n                },\n                transition: {\n                    duration: 0.3,\n                    ease: \"easeInOut\"\n                },\n                className: \"fixed top-0 left-0 h-full w-64 border-r flex flex-col z-50 \".concat(theme === \"light\" ? \"border-black bg-white\" : \"border-border bg-background\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                className: \"flex items-center gap-2 hover:text-purple-500\",\n                                onClick: goToHome,\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative h-6 w-6\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                                            src: \"https://hebbkx1anhila5yf.public.blob.vercel-storage.com/logo..-modified-dNI2SJo4cpnC8nxN30N6PW6EvffdAE.png\",\n                                            alt: \"Cognimosity Logo\",\n                                            fill: true,\n                                            className: \"object-contain\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 71,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 70,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-semibold\",\n                                        children: \"Cognimosity\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 78,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                className: \"h-8 w-8\",\n                                onClick: ()=>setIsOpen(false),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-3 py-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                            variant: \"ghost\",\n                            className: \"w-full justify-start px-4 py-2 text-sm \".concat(textColor, \" hover:text-purple-500\"),\n                            onClick: ()=>setTheme(theme === \"dark\" ? \"light\" : \"dark\"),\n                            children: theme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2 \".concat(iconColor)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 94,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Light Mode\"\n                                ]\n                            }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-4 w-4 mr-2 \".concat(iconColor)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 99,\n                                        columnNumber: 17\n                                    }, this),\n                                    \"Dark Mode\"\n                                ]\n                            }, void 0, true)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                            lineNumber: 87,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-3 py-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full px-4 py-2 \".concat(textColor, \" font-medium\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 110,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"History\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 111,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 109,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 108,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pl-6 space-y-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-sm text-neutral-500 py-2\",\n                                    children: \"No recent history\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 115,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 114,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                        lineNumber: 107,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-3 py-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full px-4 py-2 \".concat(textColor, \" font-medium\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 123,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Spaces\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 122,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pl-6 space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        variant: \"ghost\",\n                                        className: \"w-full justify-start px-4 py-2 text-sm \".concat(textColor, \" hover:text-purple-500\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2 \".concat(iconColor)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 132,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"My Space\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 128,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        variant: \"outline\",\n                                        className: \"w-full justify-start gap-2 border-dashed border-neutral-700 text-sm \".concat(textColor, \" hover:border-purple-500 hover:text-purple-500\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Add space\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 127,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"px-3 py-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-full px-4 py-2 \".concat(textColor, \" font-medium\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 149,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: \"Help & Tools\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 150,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 148,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 147,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pl-6 space-y-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        variant: \"ghost\",\n                                        className: \"w-full justify-start px-4 py-2 text-sm \".concat(textColor, \" hover:text-purple-500\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2 \".concat(iconColor)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 158,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Feedback\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 154,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        variant: \"ghost\",\n                                        className: \"w-full justify-start px-4 py-2 text-sm \".concat(textColor, \" hover:text-purple-500\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2 \".concat(iconColor)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Quick Guide\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 161,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                        variant: \"ghost\",\n                                        className: \"w-full justify-start px-4 py-2 text-sm \".concat(textColor, \" hover:text-purple-500\"),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2 \".concat(iconColor)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 15\n                                            }, this),\n                                            \"Invite & Earn\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                        lineNumber: 168,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 153,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                        lineNumber: 146,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-auto p-3 space-y-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                variant: \"outline\",\n                                className: \"w-full justify-center text-purple-500 hover:bg-purple-500/10 \".concat(theme === \"light\" ? \"border-purple-500\" : \"border-purple-500\"),\n                                children: \"Upgrade\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 179,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"rounded-md p-2 text-center \".concat(theme === \"light\" ? \"bg-white border border-black\" : \"bg-neutral-900\"),\n                                children: isLoggedIn ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    variant: \"ghost\",\n                                    className: \"w-full justify-center text-sm \".concat(textColor, \" hover:text-purple-500\"),\n                                    onClick: handleLogout,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2 \".concat(iconColor)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Log out (\",\n                                        username,\n                                        \")\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_1__.Button, {\n                                    variant: \"ghost\",\n                                    className: \"w-full justify-center text-sm \".concat(textColor, \" hover:text-purple-500\"),\n                                    onClick: handleSignIn,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_BookOpen_Clock_CuboidIcon_LogOut_Moon_Plus_Sun_ThumbsUp_Users_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                            className: \"h-4 w-4 mr-2 \".concat(iconColor)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                            lineNumber: 208,\n                                            columnNumber: 17\n                                        }, this),\n                                        \"Sign in / Sign up\"\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                                lineNumber: 188,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                        lineNumber: 178,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\sidebar.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(Sidebar, \"X9pAEMNvvatsKWVsAf27Hbp7QmM=\", false, function() {\n    return [\n        _components_theme_provider__WEBPACK_IMPORTED_MODULE_3__.useTheme,\n        next_navigation__WEBPACK_IMPORTED_MODULE_4__.useRouter\n    ];\n});\n_c = Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/sidebar.tsx\n"));

/***/ })

});