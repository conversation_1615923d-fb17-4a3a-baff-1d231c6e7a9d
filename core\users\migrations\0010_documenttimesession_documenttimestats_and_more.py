# Generated by Django 4.2.21 on 2025-06-28 06:39

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('documents', '0010_rename_questionanswer_quiz'),
        ('users', '0009_platformtimesession_platformtimestats_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='DocumentTimeSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_start', models.DateTimeField(help_text='When the session started (Indian timezone)')),
                ('session_end', models.DateTimeField(blank=True, help_text='When the session ended (Indian timezone)', null=True)),
                ('total_time_seconds', models.PositiveIntegerField(default=0, help_text='Total time spent in seconds')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this session is currently active')),
                ('last_activity', models.DateTimeField(auto_now=True, help_text='Last recorded activity')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('document', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='time_sessions', to='documents.document')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='document_sessions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Document Time Session',
                'verbose_name_plural': 'Document Time Sessions',
                'ordering': ['-session_start'],
            },
        ),
        migrations.CreateModel(
            name='DocumentTimeStats',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_time_seconds', models.PositiveIntegerField(default=0, help_text='Total time spent in seconds')),
                ('total_sessions', models.PositiveIntegerField(default=0, help_text='Total number of sessions')),
                ('first_access', models.DateTimeField(blank=True, help_text='First time accessing this document', null=True)),
                ('last_access', models.DateTimeField(blank=True, help_text='Last time accessing this document', null=True)),
                ('view_count', models.PositiveIntegerField(default=0, help_text='Number of times document was viewed')),
                ('reopened_count', models.PositiveIntegerField(default=0, help_text='Number of times document was reopened')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('document', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='time_stats', to='documents.document')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='document_stats', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Document Time Stats',
                'verbose_name_plural': 'Document Time Stats',
                'ordering': ['-total_time_seconds'],
            },
        ),
        migrations.AlterUniqueTogether(
            name='platformtimestats',
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name='platformtimestats',
            name='document',
        ),
        migrations.RemoveField(
            model_name='platformtimestats',
            name='student',
        ),
        migrations.DeleteModel(
            name='PlatformTimeSession',
        ),
        migrations.DeleteModel(
            name='PlatformTimeStats',
        ),
        migrations.AddIndex(
            model_name='documenttimestats',
            index=models.Index(fields=['student', 'document'], name='users_docum_student_baedfc_idx'),
        ),
        migrations.AddIndex(
            model_name='documenttimestats',
            index=models.Index(fields=['total_time_seconds'], name='users_docum_total_t_b80ae9_idx'),
        ),
        migrations.AddIndex(
            model_name='documenttimestats',
            index=models.Index(fields=['last_access'], name='users_docum_last_ac_9a2740_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='documenttimestats',
            unique_together={('student', 'document')},
        ),
        migrations.AddIndex(
            model_name='documenttimesession',
            index=models.Index(fields=['student', 'document'], name='users_docum_student_f76c89_idx'),
        ),
        migrations.AddIndex(
            model_name='documenttimesession',
            index=models.Index(fields=['session_start'], name='users_docum_session_b5cfa5_idx'),
        ),
        migrations.AddIndex(
            model_name='documenttimesession',
            index=models.Index(fields=['is_active'], name='users_docum_is_acti_837e6f_idx'),
        ),
        migrations.AddIndex(
            model_name='documenttimesession',
            index=models.Index(fields=['last_activity'], name='users_docum_last_ac_f4ff0d_idx'),
        ),
    ]
