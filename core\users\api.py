from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, authentication_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.authentication import TokenAuthentication
from .authentication import BearerTokenAuthentication
from .utils import get_usage_stats
from .models import PlatformTimeSession, PlatformTimeStats, PlatformTimeEvent
from .serializers import (
    PlatformTimeSessionSerializer, PlatformTimeStatsSerializer, PlatformTimeEventSerializer
)
from django.shortcuts import get_object_or_404
from django.utils import timezone
from documents.models import Document

@api_view(['GET'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication, BearerTokenAuthentication])
def validate_token(request):
    """
    Validate the authentication token and return user information.
    Accepts both Token and Bearer authentication formats.
    """
    user = request.user

    # Log successful token validation for debugging
    import logging
    logger = logging.getLogger(__name__)
    auth_header = request.META.get('HTTP_AUTHORIZATION', '')
    logger.info(f"Token validation successful for user: {user.username}, auth header: {auth_header[:15]}...")

    return Response({
        "id": user.id,
        "username": user.username,
        "email": user.email
    })

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_user_usage_stats(request, user_id):
    """
    Get usage statistics for a user
    """
    # Ensure the requesting user can only access their own stats
    if int(user_id) != request.user.id:
        return Response(
            {"error": "You can only access your own usage statistics"},
            status=status.HTTP_403_FORBIDDEN
        )

    try:
        stats = get_usage_stats(request.user)
        return Response(stats)
    except Exception as e:
        return Response(
            {"error": f"Error getting usage statistics: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([AllowAny])
def test_auth(request):
    """
    Test authentication between Django and FastAPI servers.

    This endpoint:
    1. Collects information about the current request authentication
    2. Makes a request to the FastAPI test-auth endpoint using the current user's token
    3. Returns both the local authentication info and the FastAPI response

    This helps diagnose authentication issues between the two servers.
    """
    import requests
    from django.conf import settings

    # Get the authentication header
    auth_header = request.META.get('HTTP_AUTHORIZATION', '')

    # Check if the user is authenticated
    is_authenticated = request.user.is_authenticated

    # Prepare the response with local auth info
    response_data = {
        "django_auth_info": {
            "auth_header": {
                "received": bool(auth_header),
                "value": auth_header[:10] + "..." if auth_header and len(auth_header) > 10 else auth_header,
                "format": "Unknown"
            },
            "authentication": {
                "is_authenticated": is_authenticated,
                "auth_method": str(request.auth.__class__.__name__) if request.auth else "None"
            }
        },
        "fastapi_test_results": None
    }

    # Add format information
    if auth_header:
        if auth_header.lower().startswith('token '):
            response_data["django_auth_info"]["auth_header"]["format"] = "Token"
        elif auth_header.lower().startswith('bearer '):
            response_data["django_auth_info"]["auth_header"]["format"] = "Bearer"
        else:
            response_data["django_auth_info"]["auth_header"]["format"] = "Raw (no prefix)"

    # Add user information if authenticated
    if is_authenticated:
        response_data["django_auth_info"]["user"] = {
            "id": request.user.id,
            "username": request.user.username,
            "email": request.user.email
        }

        # Get the token for the current user
        from rest_framework.authtoken.models import Token
        try:
            token, _ = Token.objects.get_or_create(user=request.user)
            token_key = token.key

            # Now test this token with the FastAPI server
            fastapi_url = getattr(settings, 'FASTAPI_URL', 'http://localhost:8001')

            # Test with different auth header formats
            auth_formats = [
                {"name": "Bearer format", "header": f"Bearer {token_key}"},
                {"name": "Token format", "header": f"Token {token_key}"},
                {"name": "Raw token", "header": token_key}
            ]

            fastapi_results = {}

            for auth_format in auth_formats:
                try:
                    # Make request to FastAPI test-auth endpoint
                    fastapi_response = requests.get(
                        f"{fastapi_url}/test-auth",
                        headers={"Authorization": auth_format["header"]},
                        timeout=5.0
                    )

                    if fastapi_response.status_code == 200:
                        fastapi_results[auth_format["name"]] = {
                            "status": "success",
                            "status_code": fastapi_response.status_code,
                            "response": fastapi_response.json()
                        }
                    else:
                        fastapi_results[auth_format["name"]] = {
                            "status": "error",
                            "status_code": fastapi_response.status_code,
                            "response": fastapi_response.text[:100] if fastapi_response.text else "No response body"
                        }
                except Exception as e:
                    fastapi_results[auth_format["name"]] = {
                        "status": "exception",
                        "error": str(e)
                    }

            response_data["fastapi_test_results"] = {
                "token_used": f"{token_key[:5]}...",
                "fastapi_url": fastapi_url,
                "results": fastapi_results
            }

        except Exception as e:
            response_data["fastapi_test_results"] = {
                "status": "error",
                "message": f"Error testing with FastAPI: {str(e)}"
            }

    return Response(response_data)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def logout_view(request):
    """
    Logout endpoint that deletes the user's auth token.
    Compatible with frontend API calls to /users/logout/
    """
    try:
        # Delete the user's token
        from rest_framework.authtoken.models import Token
        try:
            token = Token.objects.get(user=request.user)
            token.delete()
        except Token.DoesNotExist:
            pass  # Token doesn't exist, which is fine

        return Response({
            "message": "Successfully logged out"
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            "error": f"Error during logout: {str(e)}"
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Simple Platform Time Tracking API

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication, BearerTokenAuthentication])
def start_platform_session(request):
    """
    Start or resume a document learning session.
    Pauses all other active sessions for the user.
    """
    try:
        document_id = request.data.get('document_id')

        if not document_id:
            return Response({
                'error': 'document_id is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        document = get_object_or_404(Document, id=document_id, user=request.user)

        # Clean up any orphaned paused sessions first
        cleanup_count = PlatformTimeSession.cleanup_orphaned_sessions(request.user, max_age_hours=1)
        if cleanup_count > 0:
            print(f"Cleaned up {cleanup_count} orphaned sessions for user {request.user.id}")

        # End all other active sessions for this user (don't just pause them)
        other_sessions = PlatformTimeSession.objects.filter(
            student=request.user,
            is_active=True
        ).exclude(document=document)

        for session in other_sessions:
            session.end_session()
            # Create end event
            PlatformTimeEvent.objects.create(
                session=session,
                event_type='end',
                reason='New session started',
                timestamp=timezone.now().astimezone(session.get_indian_timezone())
            )

        # Check if there's already an active session for this document
        active_session = PlatformTimeSession.get_active_session(
            student=request.user,
            document=document
        )

        if active_session:
            if active_session.is_paused:
                # End the paused session and start a fresh one instead of resuming
                # This prevents accumulation of paused sessions
                active_session.end_session()
                PlatformTimeEvent.objects.create(
                    session=active_session,
                    event_type='end',
                    reason='Starting fresh session',
                    timestamp=timezone.now().astimezone(active_session.get_indian_timezone())
                )
                # Continue to create new session below
            else:
                # Session is already active, return it
                return Response({
                    'session_id': active_session.id,
                    'message': 'Session already active',
                    'is_resumed': False
                }, status=status.HTTP_200_OK)

        # Start new session
        session = PlatformTimeSession.start_session(
            student=request.user,
            document=document
        )

        # Create start event
        PlatformTimeEvent.objects.create(
            session=session,
            event_type='start',
            timestamp=session.session_start
        )

        # Update stats
        stats = PlatformTimeStats.get_or_create_stats(
            student=request.user,
            document=document
        )
        stats.increment_view_count()

        return Response({
            'session_id': session.id,
            'message': 'Session started successfully'
        }, status=status.HTTP_201_CREATED)

    except Exception as e:
        return Response({
            'error': f'Error starting session: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication, BearerTokenAuthentication])
def pause_platform_session(request):
    """
    Pause a document learning session (for quiz or navigation).
    """
    try:
        document_id = request.data.get('document_id')
        reason = request.data.get('reason', 'manual')  # 'quiz', 'navigation', 'manual'

        if not document_id:
            return Response({
                'error': 'document_id is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        document = get_object_or_404(Document, id=document_id, user=request.user)

        # Get active session for this document
        active_session = PlatformTimeSession.get_active_session(
            student=request.user,
            document=document
        )

        if not active_session:
            return Response({
                'error': 'No active session found for this document'
            }, status=status.HTTP_404_NOT_FOUND)

        if active_session.is_paused:
            return Response({
                'message': 'Session is already paused'
            }, status=status.HTTP_200_OK)

        # Pause the session
        active_session.pause_session(reason=reason)

        return Response({
            'message': 'Session paused successfully',
            'session_id': active_session.id,
            'reason': reason
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'error': f'Error pausing session: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication, BearerTokenAuthentication])
def resume_platform_session(request):
    """
    Resume a paused document learning session.
    """
    try:
        document_id = request.data.get('document_id')

        if not document_id:
            return Response({
                'error': 'document_id is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        document = get_object_or_404(Document, id=document_id, user=request.user)

        # Get active session for this document
        active_session = PlatformTimeSession.get_active_session(
            student=request.user,
            document=document
        )

        if not active_session:
            return Response({
                'error': 'No active session found for this document'
            }, status=status.HTTP_404_NOT_FOUND)

        if not active_session.is_paused:
            return Response({
                'message': 'Session is not paused'
            }, status=status.HTTP_200_OK)

        # Resume the session
        active_session.resume_session()

        return Response({
            'message': 'Session resumed successfully',
            'session_id': active_session.id
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'error': f'Error resuming session: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication, BearerTokenAuthentication])
def end_platform_session(request):
    """
    End a document learning session and calculate total time.
    """
    try:
        document_id = request.data.get('document_id')
        session_id = request.data.get('session_id')

        if not document_id and not session_id:
            return Response({
                'error': 'Either document_id or session_id is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        # Get session
        if session_id:
            session = get_object_or_404(PlatformTimeSession, id=session_id, student=request.user)
        else:
            document = get_object_or_404(Document, id=document_id, user=request.user)
            session = PlatformTimeSession.get_active_session(
                student=request.user,
                document=document
            )
            if not session:
                return Response({
                    'error': 'No active session found for this document'
                }, status=status.HTTP_404_NOT_FOUND)

        # End the session
        session.end_session()

        # Create end event
        PlatformTimeEvent.objects.create(
            session=session,
            event_type='end',
            reason='completion',
            timestamp=session.session_end
        )

        # Update stats
        stats = PlatformTimeStats.get_or_create_stats(
            student=request.user,
            document=session.document
        )
        stats.update_from_session(session)

        return Response({
            'message': 'Session ended successfully',
            'total_time_seconds': session.total_time_seconds
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'error': f'Error ending session: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication, BearerTokenAuthentication])
def get_platform_stats(request):
    """
    Get simple platform time statistics for the user.
    """
    try:
        # Get all stats for user
        all_stats = PlatformTimeStats.objects.filter(student=request.user)

        # Calculate totals
        total_time = sum(stat.total_time_seconds for stat in all_stats)
        total_sessions = sum(stat.total_sessions for stat in all_stats)
        total_documents = all_stats.filter(document__isnull=False).count()

        # Get recent sessions
        recent_sessions = PlatformTimeSession.objects.filter(
            student=request.user
        ).order_by('-session_start')[:5]

        session_data = []
        for session in recent_sessions:
            doc_title = session.document.title if session.document else "Platform"
            session_data.append({
                'id': session.id,
                'document_title': doc_title,
                'start_time': session.session_start,
                'end_time': session.session_end,
                'total_time_seconds': session.total_time_seconds
            })

        return Response({
            'overview': {
                'total_time_seconds': total_time,
                'total_sessions': total_sessions,
                'total_documents': total_documents,
            },
            'recent_sessions': session_data
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'error': f'Error getting stats: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


