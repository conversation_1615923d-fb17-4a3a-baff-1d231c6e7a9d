from rest_framework import status
from rest_framework.decorators import api_view, permission_classes, authentication_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.authentication import TokenAuthentication
from .authentication import BearerTokenAuthentication
from .utils import get_usage_stats
from .models import DocumentTimeTracking
from django.utils import timezone
import pytz

@api_view(['GET'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication, BearerTokenAuthentication])
def validate_token(request):
    """
    Validate the authentication token and return user information.
    Accepts both Token and Bearer authentication formats.
    """
    user = request.user

    # Log successful token validation for debugging
    import logging
    logger = logging.getLogger(__name__)
    auth_header = request.META.get('HTTP_AUTHORIZATION', '')
    logger.info(f"Token validation successful for user: {user.username}, auth header: {auth_header[:15]}...")

    return Response({
        "id": user.id,
        "username": user.username,
        "email": user.email
    })

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_user_usage_stats(request, user_id):
    """
    Get usage statistics for a user
    """
    # Ensure the requesting user can only access their own stats
    if int(user_id) != request.user.id:
        return Response(
            {"error": "You can only access your own usage statistics"},
            status=status.HTTP_403_FORBIDDEN
        )

    try:
        stats = get_usage_stats(request.user)
        return Response(stats)
    except Exception as e:
        return Response(
            {"error": f"Error getting usage statistics: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([AllowAny])
def test_auth(request):
    """
    Test authentication between Django and FastAPI servers.

    This endpoint:
    1. Collects information about the current request authentication
    2. Makes a request to the FastAPI test-auth endpoint using the current user's token
    3. Returns both the local authentication info and the FastAPI response

    This helps diagnose authentication issues between the two servers.
    """
    import requests
    from django.conf import settings

    # Get the authentication header
    auth_header = request.META.get('HTTP_AUTHORIZATION', '')

    # Check if the user is authenticated
    is_authenticated = request.user.is_authenticated

    # Prepare the response with local auth info
    response_data = {
        "django_auth_info": {
            "auth_header": {
                "received": bool(auth_header),
                "value": auth_header[:10] + "..." if auth_header and len(auth_header) > 10 else auth_header,
                "format": "Unknown"
            },
            "authentication": {
                "is_authenticated": is_authenticated,
                "auth_method": str(request.auth.__class__.__name__) if request.auth else "None"
            }
        },
        "fastapi_test_results": None
    }

    # Add format information
    if auth_header:
        if auth_header.lower().startswith('token '):
            response_data["django_auth_info"]["auth_header"]["format"] = "Token"
        elif auth_header.lower().startswith('bearer '):
            response_data["django_auth_info"]["auth_header"]["format"] = "Bearer"
        else:
            response_data["django_auth_info"]["auth_header"]["format"] = "Raw (no prefix)"

    # Add user information if authenticated
    if is_authenticated:
        response_data["django_auth_info"]["user"] = {
            "id": request.user.id,
            "username": request.user.username,
            "email": request.user.email
        }

        # Get the token for the current user
        from rest_framework.authtoken.models import Token
        try:
            token, _ = Token.objects.get_or_create(user=request.user)
            token_key = token.key

            # Now test this token with the FastAPI server
            fastapi_url = getattr(settings, 'FASTAPI_URL', 'http://localhost:8001')

            # Test with different auth header formats
            auth_formats = [
                {"name": "Bearer format", "header": f"Bearer {token_key}"},
                {"name": "Token format", "header": f"Token {token_key}"},
                {"name": "Raw token", "header": token_key}
            ]

            fastapi_results = {}

            for auth_format in auth_formats:
                try:
                    # Make request to FastAPI test-auth endpoint
                    fastapi_response = requests.get(
                        f"{fastapi_url}/test-auth",
                        headers={"Authorization": auth_format["header"]},
                        timeout=5.0
                    )

                    if fastapi_response.status_code == 200:
                        fastapi_results[auth_format["name"]] = {
                            "status": "success",
                            "status_code": fastapi_response.status_code,
                            "response": fastapi_response.json()
                        }
                    else:
                        fastapi_results[auth_format["name"]] = {
                            "status": "error",
                            "status_code": fastapi_response.status_code,
                            "response": fastapi_response.text[:100] if fastapi_response.text else "No response body"
                        }
                except Exception as e:
                    fastapi_results[auth_format["name"]] = {
                        "status": "exception",
                        "error": str(e)
                    }

            response_data["fastapi_test_results"] = {
                "token_used": f"{token_key[:5]}...",
                "fastapi_url": fastapi_url,
                "results": fastapi_results
            }

        except Exception as e:
            response_data["fastapi_test_results"] = {
                "status": "error",
                "message": f"Error testing with FastAPI: {str(e)}"
            }

    return Response(response_data)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def logout_view(request):
    """
    Logout endpoint that deletes the user's auth token.
    Compatible with frontend API calls to /users/logout/
    """
    try:
        # Delete the user's token
        from rest_framework.authtoken.models import Token
        try:
            token = Token.objects.get(user=request.user)
            token.delete()
        except Token.DoesNotExist:
            pass  # Token doesn't exist, which is fine

        return Response({
            "message": "Successfully logged out"
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            "error": f"Error during logout: {str(e)}"
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


# Simple Document Time Tracking API

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication, BearerTokenAuthentication])
def start_document_timer(request):
    """
    Start timer for a document (when entering 2nd webpage).
    Returns session start time for frontend tracking.
    """
    try:
        file_name = request.data.get('file_name')

        if not file_name:
            return Response({
                'error': 'file_name is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        username = request.user.username

        # Get or create tracking record
        tracking = DocumentTimeTracking.get_or_create_tracking(username, file_name)

        # Return session start time for frontend tracking
        indian_tz = DocumentTimeTracking.get_indian_timezone()
        session_start = timezone.now().astimezone(indian_tz)

        return Response({
            'message': 'Timer started successfully',
            'session_start': session_start.isoformat(),
            'tracking_id': tracking.id
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'error': f'Error starting timer: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication, BearerTokenAuthentication])
def stop_document_timer(request):
    """
    Stop timer for a document (when leaving 2nd webpage).
    Adds the session time to total time.
    """
    try:
        file_name = request.data.get('file_name')
        session_time_seconds = request.data.get('session_time_seconds', 0)

        if not file_name:
            return Response({
                'error': 'file_name is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        username = request.user.username

        # Get tracking record
        try:
            tracking = DocumentTimeTracking.objects.get(username=username, file_name=file_name)
        except DocumentTimeTracking.DoesNotExist:
            return Response({
                'error': 'No tracking record found for this file'
            }, status=status.HTTP_404_NOT_FOUND)

        # Add session time to total
        if session_time_seconds > 0:
            tracking.add_session_time(session_time_seconds)

        return Response({
            'message': 'Timer stopped successfully',
            'total_time_seconds': tracking.total_time_seconds,
            'total_time_formatted': tracking.total_time_formatted,
            'number_of_sessions': tracking.number_of_sessions
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'error': f'Error stopping timer: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication, BearerTokenAuthentication])
def increment_quiz_count(request):
    """
    Increment quiz count for a document.
    """
    try:
        file_name = request.data.get('file_name')

        if not file_name:
            return Response({
                'error': 'file_name is required'
            }, status=status.HTTP_400_BAD_REQUEST)

        username = request.user.username

        # Get tracking record
        try:
            tracking = DocumentTimeTracking.objects.get(username=username, file_name=file_name)
        except DocumentTimeTracking.DoesNotExist:
            return Response({
                'error': 'No tracking record found for this file'
            }, status=status.HTTP_404_NOT_FOUND)

        # Increment quiz count
        tracking.increment_quiz_count()

        return Response({
            'message': 'Quiz count incremented successfully',
            'number_of_quizzes': tracking.number_of_quizzes
        }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'error': f'Error incrementing quiz count: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
@authentication_classes([TokenAuthentication, BearerTokenAuthentication])
def get_document_stats(request):
    """
    Get time tracking stats for a specific document or all documents.
    """
    try:
        file_name = request.GET.get('file_name')
        username = request.user.username

        if file_name:
            # Get stats for specific file
            try:
                tracking = DocumentTimeTracking.objects.get(username=username, file_name=file_name)
                return Response({
                    'username': tracking.username,
                    'file_name': tracking.file_name,
                    'total_time_seconds': tracking.total_time_seconds,
                    'total_time_formatted': tracking.total_time_formatted,
                    'number_of_sessions': tracking.number_of_sessions,
                    'number_of_quizzes': tracking.number_of_quizzes,
                    'average_session_time': tracking.average_session_time,
                    'last_accessed': tracking.last_accessed.isoformat(),
                    'created_at': tracking.created_at.isoformat()
                }, status=status.HTTP_200_OK)
            except DocumentTimeTracking.DoesNotExist:
                return Response({
                    'error': 'No tracking record found for this file'
                }, status=status.HTTP_404_NOT_FOUND)
        else:
            # Get stats for all files
            trackings = DocumentTimeTracking.objects.filter(username=username).order_by('-last_accessed')

            stats_data = []
            for tracking in trackings:
                stats_data.append({
                    'username': tracking.username,
                    'file_name': tracking.file_name,
                    'total_time_seconds': tracking.total_time_seconds,
                    'total_time_formatted': tracking.total_time_formatted,
                    'number_of_sessions': tracking.number_of_sessions,
                    'number_of_quizzes': tracking.number_of_quizzes,
                    'average_session_time': tracking.average_session_time,
                    'last_accessed': tracking.last_accessed.isoformat(),
                    'created_at': tracking.created_at.isoformat()
                })

            # Calculate totals
            total_time = sum(t.total_time_seconds for t in trackings)
            total_sessions = sum(t.number_of_sessions for t in trackings)
            total_quizzes = sum(t.number_of_quizzes for t in trackings)

            return Response({
                'files': stats_data,
                'summary': {
                    'total_files': trackings.count(),
                    'total_time_seconds': total_time,
                    'total_sessions': total_sessions,
                    'total_quizzes': total_quizzes
                }
            }, status=status.HTTP_200_OK)

    except Exception as e:
        return Response({
            'error': f'Error getting stats: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)



