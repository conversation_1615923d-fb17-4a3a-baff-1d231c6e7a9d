from django.core.management.base import BaseCommand
from django.utils import timezone
from users.models import DocumentTimeSession


class Command(BaseCommand):
    help = 'Cleanup abandoned time tracking sessions'

    def add_arguments(self, parser):
        parser.add_argument(
            '--timeout',
            type=int,
            default=30,
            help='Timeout in minutes for considering a session abandoned (default: 30)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be cleaned up without actually doing it'
        )

    def handle(self, *args, **options):
        timeout_minutes = options['timeout']
        dry_run = options['dry_run']
        
        self.stdout.write(
            self.style.SUCCESS(f'Looking for sessions abandoned for more than {timeout_minutes} minutes...')
        )
        
        cutoff_time = timezone.now() - timezone.timedelta(minutes=timeout_minutes)
        
        # Find sessions to be marked as abandoned
        sessions_to_abandon = DocumentTimeSession.objects.filter(
            status__in=['active', 'paused'],
            last_activity__lt=cutoff_time
        )
        
        count = sessions_to_abandon.count()
        
        if count == 0:
            self.stdout.write(
                self.style.SUCCESS('No abandoned sessions found.')
            )
            return
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING(f'DRY RUN: Would mark {count} sessions as abandoned')
            )
            for session in sessions_to_abandon[:10]:  # Show first 10
                self.stdout.write(
                    f'  - Session {session.id}: {session.student.username} - {session.document.title} '
                    f'(last activity: {session.last_activity})'
                )
            if count > 10:
                self.stdout.write(f'  ... and {count - 10} more sessions')
        else:
            # Actually mark sessions as abandoned
            updated_count = sessions_to_abandon.update(status='abandoned')
            self.stdout.write(
                self.style.SUCCESS(f'Successfully marked {updated_count} sessions as abandoned')
            )
