from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from rest_framework.authtoken.views import obtain_auth_token
from .views import (
    StudentViewSet,
    StudentRegistrationView,
    EmailVerificationView,
    OTPVerificationView,
    get_usage,
    GoogleSignInView,
    StudentPerformanceViewSet
)
from .api import (
    validate_token, get_user_usage_stats, test_auth, logout_view,
    start_document_timer, stop_document_timer, increment_quiz_count, get_document_stats
)

router = DefaultRouter()
router.register(r'students', StudentViewSet)
router.register(r'performance', StudentPerformanceViewSet, basename='student-performance')

urlpatterns = [
    # Original routes
    path('register/', StudentRegistrationView.as_view(), name='register'),
    path('verify-email/<str:token>/', EmailVerificationView.as_view(), name='verify-email'),
    path('verify-otp/', OTPVerificationView.as_view(), name='verify-otp'),
    path('', include(router.urls)),
    path('login/', obtain_auth_token, name='api_token_auth'),
    path('logout/', logout_view, name='api_logout'),
    path('usage/', get_usage, name='get-usage'),
    path('auth/google/', GoogleSignInView.as_view(), name='google-signin'),

    # API endpoints for FastAPI integration
    path('auth/validate-token/', validate_token, name='validate-token'),
    path('<int:user_id>/usage-stats/', get_user_usage_stats, name='get-user-usage-stats'),
    path('auth/test/', test_auth, name='test-auth'),

    # Simple document time tracking endpoints
    path('timer/start/', start_document_timer, name='start-document-timer'),
    path('timer/stop/', stop_document_timer, name='stop-document-timer'),
    path('timer/quiz/', increment_quiz_count, name='increment-quiz-count'),
    path('timer/stats/', get_document_stats, name='get-document-stats'),
]