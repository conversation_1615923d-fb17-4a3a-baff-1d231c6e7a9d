# Generated manually to add DocumentTimeTracking model

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0011_platformtimeevent_platformtimesession_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='DocumentTimeTracking',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('username', models.Char<PERSON>ield(help_text='Username of the student', max_length=150)),
                ('file_name', models.Char<PERSON>ield(help_text='Name of the document file', max_length=255)),
                ('total_time_seconds', models.PositiveIntegerField(default=0, help_text='Total time spent on 2nd webpage in seconds')),
                ('number_of_sessions', models.PositiveIntegerField(default=0, help_text='Number of times the 2nd webpage was accessed')),
                ('number_of_quizzes', models.PositiveIntegerField(default=0, help_text='Number of quizzes taken for this file')),
                ('last_accessed', models.DateTimeField(auto_now=True, help_text='Last time the 2nd webpage was accessed (Indian timezone)')),
                ('created_at', models.DateTimeField(auto_now_add=True, help_text='When this record was first created')),
            ],
            options={
                'verbose_name': 'Document Time Tracking',
                'verbose_name_plural': 'Document Time Tracking',
                'ordering': ['-last_accessed'],
            },
        ),
        migrations.AddIndex(
            model_name='documenttimetracking',
            index=models.Index(fields=['username', 'file_name'], name='users_docum_usernam_c66d59_idx'),
        ),
        migrations.AddIndex(
            model_name='documenttimetracking',
            index=models.Index(fields=['last_accessed'], name='users_docum_last_ac_db8f4d_idx'),
        ),
        migrations.AddIndex(
            model_name='documenttimetracking',
            index=models.Index(fields=['total_time_seconds'], name='users_docum_total_t_a849da_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='documenttimetracking',
            unique_together={('username', 'file_name')},
        ),
    ]
