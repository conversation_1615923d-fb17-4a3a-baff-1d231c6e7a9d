# Automatic Learning Time Prediction System

## Overview

The system now automatically generates learning time predictions for every uploaded document after processing is completed. This eliminates the need for manual API calls to generate predictions.

## How It Works

### 1. Document Upload Flow
```
User uploads document → Document processing → Embeddings creation → Learning time prediction (AUTOMATIC)
```

### 2. Automatic Trigger Points

**After Document Processing (FastAPI):**
- When `process-document/{document_id}` completes successfully
- Automatically calls `trigger_learning_time_prediction()`
- Sends request to Django to start learning time generation

**After Embeddings Creation (Django Celery):**
- When `process_document_task` completes successfully
- Automatically triggers `generate_learning_time_task.delay()`
- Generates learning time prediction in background

## Implementation Details

### New Components Added

#### 1. Celery Task (`core/documents/tasks.py`)
```python
@shared_task(bind=True, max_retries=1, default_retry_delay=300)
def generate_learning_time_task(self, document_id: int, auth_token: str, llm_model: str = "gemini"):
    """
    Automatically generates learning time prediction after document processing.
    - Checks if document is completed and has embeddings
    - Calls FastAPI learning time prediction endpoint
    - Saves results to database
    """
```

#### 2. FastAPI Integration (`llm_api/main.py`)
```python
async def trigger_learning_time_prediction(document_id: int, user: UserInfo):
    """
    Triggers learning time prediction after document processing.
    Called automatically from process_document endpoint.
    """
```

#### 3. Django Trigger Endpoint (`core/documents/views.py`)
```python
@action(detail=True, methods=['post'], url_path='trigger-learning-time')
def trigger_learning_time(self, request, pk=None):
    """
    Endpoint called by FastAPI to start learning time generation.
    Starts Celery task or falls back to direct generation.
    """
```

### Modified Components

#### 1. Document Processing Task
- Added automatic trigger for learning time prediction
- Triggers after successful document processing and embedding creation

#### 2. FastAPI Document Processing
- Added automatic call to trigger learning time prediction
- Non-blocking - doesn't fail document processing if prediction fails

## Database Schema

### DocumentLearningTime Model
```python
class DocumentLearningTime(models.Model):
    document = models.OneToOneField(Document, related_name='learning_time')
    predicted_time_seconds = models.PositiveIntegerField()
    topic_difficulty = models.IntegerField(choices=DIFFICULTY_CHOICES)  # 1-5
    content_length_words = models.PositiveIntegerField()
    concept_density = models.IntegerField(choices=CONCEPT_DENSITY_CHOICES)  # 1-5
    analysis_factors = models.JSONField(default=dict)
    gemini_reasoning = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
```

## API Endpoints

### Automatic Endpoints (Internal)
- `POST /api/documents/{id}/trigger-learning-time/` - Triggered by FastAPI
- `POST /predict-learning-time/{document_id}` - FastAPI prediction endpoint
- `POST /api/documents/{id}/save-learning-time/` - Save prediction to database

### Manual Endpoints (Optional)
- `GET /api/documents/{id}/learning-time/` - Get existing prediction
- `POST /api/documents/{id}/learning-time/` - Force regenerate prediction

## Configuration

### Environment Variables
```bash
FASTAPI_URL=http://localhost:8001  # FastAPI server URL
GOOGLE_API_KEY=your_gemini_api_key  # Gemini AI API key
```

### Celery Configuration
- Task: `generate_learning_time_task`
- Max retries: 1
- Retry delay: 5 minutes
- Queue: default

## Usage

### For Users
1. Upload any document through the frontend
2. Wait for processing to complete
3. Learning time prediction is automatically generated
4. View predictions in Django admin or through API

### For Developers
```python
# Check if prediction exists
learning_time = DocumentLearningTime.objects.filter(document=document).first()

# Get prediction data
if learning_time:
    print(f"Predicted time: {learning_time.predicted_time_seconds} seconds")
    print(f"Time range: {learning_time.get_time_range_display()}")
    print(f"Difficulty: {learning_time.get_topic_difficulty_display()}")
```

## Admin Interface

### Django Admin
- Navigate to `/admin/`
- Look for "Document learning times" under Documents section
- View all predictions with filtering and search capabilities

### Admin Features
- List view with key metrics
- Filtering by difficulty and concept density
- Search by document title and reasoning
- Organized fieldsets for easy viewing

## Error Handling

### Graceful Degradation
- Document processing never fails due to learning time prediction errors
- Prediction generation is non-blocking and happens in background
- Automatic retries for failed predictions
- Fallback to direct generation if Celery fails

### Logging
- All prediction attempts are logged
- Errors are captured but don't affect document processing
- Success/failure status is tracked

## Testing

### Test Scripts
- `test_learning_time_prediction.py` - Basic model and API testing
- `test_learning_time_backend.py` - Backend storage verification
- `test_automatic_learning_time.py` - Automatic generation testing

### Manual Testing
1. Start both Django and FastAPI servers
2. Upload a document
3. Wait for processing completion
4. Check Django admin for automatic prediction
5. Verify prediction data is complete and accurate

## Performance Considerations

### Optimization
- Predictions are generated asynchronously
- Cached to avoid regeneration
- Only generated for completed documents with embeddings
- Background processing doesn't block user interface

### Resource Usage
- Gemini API calls are rate-limited
- Celery tasks run in background workers
- Database queries are optimized with select_related

## Future Enhancements

### Planned Features
- User-specific learning rate adjustments
- Historical accuracy tracking
- Adaptive prediction improvements
- Integration with actual learning time tracking

### Possible Improvements
- Machine learning model training on actual vs predicted times
- Subject-specific prediction models
- User feedback integration for prediction accuracy
