"use client"

import React from "react"
import { useSearch<PERSON>ara<PERSON>, useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowLeft } from "lucide-react"
import { QuizInterface } from "@/components/quiz-interface"
import { LayoutWithSidebar } from "@/components/layout-with-sidebar"
import { useTheme } from "@/components/theme-provider"

export default function QuizPage() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const { theme } = useTheme()
  const documentId = searchParams.get("documentId") ? parseInt(searchParams.get("documentId")!) : undefined

  // Simple quiz functions - timer stops when on quiz page, no need for pause/resume
  const startQuiz = async () => {
    console.log('Quiz started - timer automatically stopped (not on 2nd webpage)')
  }

  const endQuiz = async () => {
    // Increment quiz count when quiz is completed
    if (documentId) {
      try {
        const token = localStorage.getItem('token')
        if (!token) return

        // Get document details to get file name
        const docResponse = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api'}/documents/${documentId}/`, {
          headers: {
            'Authorization': `Token ${token}`,
          },
        })

        if (docResponse.ok) {
          const docData = await docResponse.json()
          const fileName = docData.title

          // Increment quiz count
          await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api'}/users/timer/quiz/`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Token ${token}`,
            },
            body: JSON.stringify({ file_name: fileName }),
          })

          console.log('Quiz count incremented for file:', fileName)
        }
      } catch (error) {
        console.error('Error incrementing quiz count:', error)
      }
    }

    console.log('Quiz ended - timer will resume when returning to 2nd webpage')
  }

  const handleBackToDocument = () => {
    // Resume the session when going back to document
    endQuiz()

    if (documentId) {
      router.push(`/process?documentId=${documentId}`)
    } else {
      router.push("/")
    }
  }

  if (!documentId) {
    return (
      <LayoutWithSidebar>
        <div className="h-[calc(100vh-65px)] flex items-center justify-center">
          <div className={`rounded-lg p-8 text-center ${theme === "light" ? "bg-white border border-black" : "bg-neutral-800"}`}>
            <h2 className="text-xl font-semibold mb-4">No Document Selected</h2>
            <p className="text-muted-foreground mb-4">
              Please select a document to take a quiz.
            </p>
            <Button onClick={() => router.push("/")} className="bg-primary hover:bg-primary/90">
              Go to Home
            </Button>
          </div>
        </div>
      </LayoutWithSidebar>
    )
  }

  return (
    <LayoutWithSidebar showUpgradeButton={true}>
      <div className="h-[calc(100vh-65px)] flex flex-col bg-background text-foreground">
        {/* Header */}
        <div className="border-b border-border p-4">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBackToDocument}
              className="gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back to Document
            </Button>
            <h1 className="text-xl font-semibold">Quiz</h1>
          </div>
        </div>

        {/* Quiz Content */}
        <div className="flex-1 overflow-hidden">
          <QuizInterface
            documentId={documentId}
            onQuizStart={startQuiz}
            onQuizEnd={endQuiz}
          />
        </div>
      </div>
    </LayoutWithSidebar>
  )
}
