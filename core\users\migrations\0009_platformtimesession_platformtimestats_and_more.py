# Generated by Django 4.2.21 on 2025-06-25 10:06

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('documents', '0010_rename_questionanswer_quiz'),
        ('users', '0008_documenttimesession_documenttimeinterval_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='PlatformTimeSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_start', models.DateTimeField(help_text='When the session started (Indian timezone)')),
                ('session_end', models.DateTimeField(blank=True, help_text='When the session ended (Indian timezone)', null=True)),
                ('total_time_seconds', models.PositiveIntegerField(default=0, help_text='Total time spent in seconds')),
                ('last_activity', models.DateTimeField(auto_now=True, help_text='Last recorded activity')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('document', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='time_sessions', to='documents.document')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='platform_sessions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Platform Time Session',
                'verbose_name_plural': 'Platform Time Sessions',
                'ordering': ['-session_start'],
            },
        ),
        migrations.CreateModel(
            name='PlatformTimeStats',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_time_seconds', models.PositiveIntegerField(default=0, help_text='Total time spent in seconds')),
                ('total_sessions', models.PositiveIntegerField(default=0, help_text='Total number of sessions')),
                ('first_access', models.DateTimeField(blank=True, help_text='First time accessed', null=True)),
                ('last_access', models.DateTimeField(blank=True, help_text='Last time accessed', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('document', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='time_stats', to='documents.document')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='platform_time_stats', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Platform Time Statistics',
                'verbose_name_plural': 'Platform Time Statistics',
                'ordering': ['-last_access'],
            },
        ),
        migrations.RemoveField(
            model_name='documenttimesession',
            name='document',
        ),
        migrations.RemoveField(
            model_name='documenttimesession',
            name='student',
        ),
        migrations.AlterUniqueTogether(
            name='documenttimestats',
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name='documenttimestats',
            name='document',
        ),
        migrations.RemoveField(
            model_name='documenttimestats',
            name='student',
        ),
        migrations.DeleteModel(
            name='DocumentTimeInterval',
        ),
        migrations.DeleteModel(
            name='DocumentTimeSession',
        ),
        migrations.DeleteModel(
            name='DocumentTimeStats',
        ),
        migrations.AddIndex(
            model_name='platformtimestats',
            index=models.Index(fields=['student', 'document'], name='users_platf_student_a71a97_idx'),
        ),
        migrations.AddIndex(
            model_name='platformtimestats',
            index=models.Index(fields=['total_time_seconds'], name='users_platf_total_t_1d632c_idx'),
        ),
        migrations.AddIndex(
            model_name='platformtimestats',
            index=models.Index(fields=['last_access'], name='users_platf_last_ac_b3d5a8_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='platformtimestats',
            unique_together={('student', 'document')},
        ),
        migrations.AddIndex(
            model_name='platformtimesession',
            index=models.Index(fields=['student', 'document'], name='users_platf_student_7e9574_idx'),
        ),
        migrations.AddIndex(
            model_name='platformtimesession',
            index=models.Index(fields=['session_start'], name='users_platf_session_2f6e60_idx'),
        ),
        migrations.AddIndex(
            model_name='platformtimesession',
            index=models.Index(fields=['last_activity'], name='users_platf_last_ac_d6da23_idx'),
        ),
    ]
