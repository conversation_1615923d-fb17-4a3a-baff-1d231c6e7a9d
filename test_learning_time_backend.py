#!/usr/bin/env python3
"""
Test script to verify that learning time predictions are being saved to the backend.

This script tests:
1. Creating a document
2. Generating learning time prediction
3. Verifying it's saved in the database
4. Testing the Django admin interface
"""

import os
import sys
import django
from django.conf import settings

# Add the core directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'core'))

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

from documents.models import Document, DocumentLearningTime, DocumentEmbedding
from users.models import Student
from django.contrib.admin.sites import site


def test_backend_saving():
    """Test that learning time predictions are properly saved to the backend"""
    print("Testing Learning Time Prediction Backend Saving...")
    print("=" * 60)
    
    try:
        # Create or get a test student
        student, created = Student.objects.get_or_create(
            username='test_backend_user',
            defaults={
                'email': '<EMAIL>'
            }
        )
        print(f"✓ Test student: {student.email} ({'created' if created else 'existing'})")
        
        # Create or get a test document
        document, created = Document.objects.get_or_create(
            user=student,
            title='Backend Test Document',
            defaults={
                'file': 'backend_test.pdf',
                'processing_status': 'completed'
            }
        )
        print(f"✓ Test document: {document.title} ({'created' if created else 'existing'})")
        
        # Create some dummy embeddings for the document (required for prediction)
        if not document.embeddings.exists():
            for i in range(3):
                DocumentEmbedding.objects.create(
                    document=document,
                    text_chunk=f"This is test chunk {i+1} with some sample content for learning time prediction.",
                    embedding=[0.1] * 384,  # Dummy embedding vector
                    chunk_number=i
                )
            print(f"✓ Created {document.embeddings.count()} test embeddings")
        else:
            print(f"✓ Document has {document.embeddings.count()} existing embeddings")
        
        # Create or update learning time prediction
        learning_time, created = DocumentLearningTime.objects.update_or_create(
            document=document,
            defaults={
                'predicted_time_seconds': 2700,  # 45 minutes
                'topic_difficulty': 2,  # Easy
                'content_length_words': 1500,
                'concept_density': 2,  # Low-Medium
                'analysis_factors': {
                    'subject_area': 'general',
                    'formula_count': 0,
                    'example_problems': 3,
                    'prerequisite_level': 'beginner'
                },
                'gemini_reasoning': 'This is a test document with basic concepts. The content is straightforward and should take about 45 minutes to learn for students aged 16-22.'
            }
        )
        
        print(f"✓ Learning time prediction: {'created' if created else 'updated'}")
        print(f"  - Document: {learning_time.document.title}")
        print(f"  - Predicted time: {learning_time.predicted_time_seconds} seconds ({learning_time.predicted_time_seconds // 60} minutes)")
        print(f"  - Time range: {learning_time.get_time_range_display()}")
        print(f"  - Difficulty: {learning_time.get_topic_difficulty_display()}")
        print(f"  - Concept density: {learning_time.get_concept_density_display()}")
        print(f"  - Analysis factors: {learning_time.analysis_factors}")
        
        # Verify the record exists in the database
        total_records = DocumentLearningTime.objects.count()
        print(f"✓ Total learning time records in database: {total_records}")
        
        # Test admin registration
        admin_models = [model._meta.label for model in site._registry.keys()]
        learning_time_in_admin = 'documents.DocumentLearningTime' in admin_models
        print(f"✓ DocumentLearningTime registered in admin: {learning_time_in_admin}")
        
        if learning_time_in_admin:
            print("  - The model should now appear in Django admin interface")
            print("  - Restart the Django server if you don't see it")
        else:
            print("  - ⚠️  Model not registered in admin - check admin.py")
        
        # Test the serializer
        from documents.serializers import DocumentLearningTimeSerializer
        serializer = DocumentLearningTimeSerializer(learning_time)
        serialized_data = serializer.data
        print(f"✓ Serialization successful: {len(serialized_data)} fields")
        print(f"  - Serialized time range: {serialized_data.get('time_range_display')}")
        
        return True
        
    except Exception as e:
        print(f"✗ Backend saving test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_database_queries():
    """Test various database queries for learning time predictions"""
    print("\nTesting Database Queries...")
    print("=" * 40)
    
    try:
        # Get all learning time predictions
        all_predictions = DocumentLearningTime.objects.all()
        print(f"✓ Total predictions: {all_predictions.count()}")
        
        # Get predictions by difficulty
        for difficulty in range(1, 6):
            count = DocumentLearningTime.objects.filter(topic_difficulty=difficulty).count()
            if count > 0:
                difficulty_name = dict(DocumentLearningTime.DIFFICULTY_CHOICES)[difficulty]
                print(f"  - {difficulty_name}: {count} predictions")
        
        # Get predictions by concept density
        for density in range(1, 6):
            count = DocumentLearningTime.objects.filter(concept_density=density).count()
            if count > 0:
                density_name = dict(DocumentLearningTime.CONCEPT_DENSITY_CHOICES)[density]
                print(f"  - {density_name}: {count} predictions")
        
        # Get recent predictions
        recent_predictions = DocumentLearningTime.objects.order_by('-created_at')[:5]
        print(f"✓ Recent predictions:")
        for pred in recent_predictions:
            print(f"  - {pred.document.title}: {pred.predicted_time_seconds}s ({pred.get_topic_difficulty_display()})")
        
        return True
        
    except Exception as e:
        print(f"✗ Database queries test failed: {str(e)}")
        return False


def main():
    """Run all backend tests"""
    print("Learning Time Prediction Backend Test")
    print("=" * 60)
    
    tests = [
        test_backend_saving,
        test_database_queries
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"Backend Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All backend tests passed!")
        print("\nTo see the learning time predictions in Django admin:")
        print("1. Restart the Django server: cd core && python manage.py runserver")
        print("2. Go to http://localhost:8000/admin/")
        print("3. Look for 'Document learning times' under the Documents section")
        print("4. You should see the learning time predictions listed there")
    else:
        print("❌ Some backend tests failed. Please check the errors above.")
    
    print("=" * 60)


if __name__ == '__main__':
    main()
