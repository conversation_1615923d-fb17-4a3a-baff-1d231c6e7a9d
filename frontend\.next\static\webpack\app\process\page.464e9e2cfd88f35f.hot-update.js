"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/process/page",{

/***/ "(app-pages-browser)/./components/layout-with-sidebar.tsx":
/*!********************************************!*\
  !*** ./components/layout-with-sidebar.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LayoutWithSidebar: () => (/* binding */ LayoutWithSidebar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_sidebar__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/sidebar */ \"(app-pages-browser)/./components/sidebar.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Crown_HelpCircle_LogOut_Menu_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Crown,HelpCircle,LogOut,Menu,Moon,Sun!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/menu.js\");\n/* harmony import */ var _barrel_optimize_names_Crown_HelpCircle_LogOut_Menu_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Crown,HelpCircle,LogOut,Menu,Moon,Sun!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _barrel_optimize_names_Crown_HelpCircle_LogOut_Menu_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Crown,HelpCircle,LogOut,Menu,Moon,Sun!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/circle-help.js\");\n/* harmony import */ var _barrel_optimize_names_Crown_HelpCircle_LogOut_Menu_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Crown,HelpCircle,LogOut,Menu,Moon,Sun!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/crown.js\");\n/* harmony import */ var _barrel_optimize_names_Crown_HelpCircle_LogOut_Menu_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Crown,HelpCircle,LogOut,Menu,Moon,Sun!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sun.js\");\n/* harmony import */ var _barrel_optimize_names_Crown_HelpCircle_LogOut_Menu_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Crown,HelpCircle,LogOut,Menu,Moon,Sun!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/moon.js\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/theme-provider */ \"(app-pages-browser)/./components/theme-provider.tsx\");\n/* __next_internal_client_entry_do_not_use__ LayoutWithSidebar auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction LayoutWithSidebar(param) {\n    let { children, showQuizButton = false, showUpgradeButton = false, showUserEmail = false, documentId = null } = param;\n    _s();\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { theme, setTheme } = (0,_components_theme_provider__WEBPACK_IMPORTED_MODULE_6__.useTheme)();\n    const [isLoggedIn, setIsLoggedIn] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [username, setUsername] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LayoutWithSidebar.useEffect\": ()=>{\n            // Check if user is logged in from localStorage or cookies\n            const userLoggedIn = localStorage.getItem(\"isLoggedIn\") === \"true\";\n            const storedUsername = localStorage.getItem(\"username\");\n            if (userLoggedIn && storedUsername) {\n                setIsLoggedIn(true);\n                setUsername(storedUsername);\n            }\n        }\n    }[\"LayoutWithSidebar.useEffect\"], []);\n    const handleLogout = ()=>{\n        // In a real app, you would call your logout API here\n        localStorage.removeItem(\"isLoggedIn\");\n        localStorage.removeItem(\"username\");\n        setIsLoggedIn(false);\n        setUsername(\"\");\n        router.push(\"/\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-background text-foreground overflow-hidden\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_sidebar__WEBPACK_IMPORTED_MODULE_3__.Sidebar, {\n                isOpen: sidebarOpen,\n                setIsOpen: setSidebarOpen,\n                isLoggedIn: isLoggedIn,\n                username: username,\n                onLogout: handleLogout\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\layout-with-sidebar.tsx\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"p-4 flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                variant: \"ghost\",\n                                size: \"icon\",\n                                className: \"h-10 w-10 rounded-full\",\n                                onClick: ()=>setSidebarOpen(true),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_HelpCircle_LogOut_Menu_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                    className: \"h-5 w-5\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\layout-with-sidebar.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\layout-with-sidebar.tsx\",\n                                lineNumber: 69,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-2\",\n                                children: [\n                                    showUserEmail && isLoggedIn && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenu, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuTrigger, {\n                                                asChild: true,\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                                    className: \"bg-purple-600 hover:bg-purple-700\",\n                                                    children: username\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\layout-with-sidebar.tsx\",\n                                                    lineNumber: 78,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\layout-with-sidebar.tsx\",\n                                                lineNumber: 77,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuContent, {\n                                                align: \"end\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_5__.DropdownMenuItem, {\n                                                    onClick: handleLogout,\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_HelpCircle_LogOut_Menu_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                            className: \"mr-2 h-4 w-4\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\layout-with-sidebar.tsx\",\n                                                            lineNumber: 82,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: \"Log out\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\layout-with-sidebar.tsx\",\n                                                            lineNumber: 83,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\layout-with-sidebar.tsx\",\n                                                    lineNumber: 81,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\layout-with-sidebar.tsx\",\n                                                lineNumber: 80,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\layout-with-sidebar.tsx\",\n                                        lineNumber: 76,\n                                        columnNumber: 15\n                                    }, this),\n                                    showQuizButton && documentId && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: ()=>router.push(\"/quiz?documentId=\".concat(documentId)),\n                                        variant: \"outline\",\n                                        size: \"sm\",\n                                        className: \"gap-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_HelpCircle_LogOut_Menu_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\layout-with-sidebar.tsx\",\n                                                lineNumber: 97,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Quiz\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\layout-with-sidebar.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 15\n                                    }, this),\n                                    showUpgradeButton && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        onClick: ()=>router.push(\"/subscription\"),\n                                        className: \"bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white border-0\",\n                                        size: \"sm\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_HelpCircle_LogOut_Menu_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 mr-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\layout-with-sidebar.tsx\",\n                                                lineNumber: 109,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Upgrade\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\layout-with-sidebar.tsx\",\n                                        lineNumber: 104,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_4__.Button, {\n                                        variant: \"ghost\",\n                                        size: \"icon\",\n                                        className: \"h-10 w-10 rounded-full\",\n                                        onClick: ()=>setTheme(theme === \"dark\" ? \"light\" : \"dark\"),\n                                        children: theme === \"dark\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_HelpCircle_LogOut_Menu_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\layout-with-sidebar.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 35\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Crown_HelpCircle_LogOut_Menu_Moon_Sun_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                            className: \"h-5 w-5\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\layout-with-sidebar.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 65\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\layout-with-sidebar.tsx\",\n                                        lineNumber: 115,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\layout-with-sidebar.tsx\",\n                                lineNumber: 73,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\layout-with-sidebar.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    children\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\layout-with-sidebar.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\cognimosity_UI\\\\latest repo\\\\cogni_api\\\\frontend\\\\components\\\\layout-with-sidebar.tsx\",\n        lineNumber: 56,\n        columnNumber: 5\n    }, this);\n}\n_s(LayoutWithSidebar, \"cHRvZTvj/5la701cJ3lhkqtn5g4=\", false, function() {\n    return [\n        _components_theme_provider__WEBPACK_IMPORTED_MODULE_6__.useTheme,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter\n    ];\n});\n_c = LayoutWithSidebar;\nvar _c;\n$RefreshReg$(_c, \"LayoutWithSidebar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/layout-with-sidebar.tsx\n"));

/***/ })

});