#!/usr/bin/env python3
"""
Test the save learning time endpoint directly.
"""

import requests
import json

def test_save_learning_time():
    """Test saving learning time prediction to Django"""
    
    document_id = 81
    auth_token = "458168605e4bfaff26ac7bce4238ba4f813ae021"
    
    # Sample learning time data
    learning_time_data = {
        "document_id": document_id,
        "predicted_time_seconds": 7200,  # 2 hours
        "topic_difficulty": 4,
        "content_length_words": 4853,
        "concept_density": 5,
        "analysis_factors": {
            "subject_area": "artificial intelligence",
            "formula_count": 20,
            "example_problems": 2,
            "prerequisite_level": "advanced undergraduate"
        },
        "gemini_reasoning": "This content covers advanced AI concepts including reinforcement learning algorithms, mathematical formulations, and complex problem-solving techniques. The high concept density and mathematical content require significant study time for students aged 16-22."
    }
    
    print("Testing save learning time endpoint...")
    print(f"Document ID: {document_id}")
    print(f"Data: {json.dumps(learning_time_data, indent=2)}")
    
    # Test the Django save endpoint
    url = f"http://localhost:8000/api/documents/{document_id}/save-learning-time/"
    headers = {
        "Authorization": f"Token {auth_token}",
        "Content-Type": "application/json"
    }
    
    response = requests.post(url, json=learning_time_data, headers=headers)
    
    print(f"\nSave endpoint response: {response.status_code}")
    print(f"Response: {response.text}")
    
    if response.status_code == 200:
        print("✓ Learning time saved successfully!")
        
        # Verify it was saved
        import os, sys, django
        from pathlib import Path
        
        # Set up Django
        core_path = str(Path(__file__).parent / 'core')
        sys.path.insert(0, core_path)
        original_cwd = os.getcwd()
        os.chdir(core_path)
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
        django.setup()
        os.chdir(original_cwd)
        
        from documents.models import DocumentLearningTime
        
        learning_time = DocumentLearningTime.objects.filter(document_id=document_id).first()
        if learning_time:
            print(f"✓ Verified in database: {learning_time.predicted_time_seconds} seconds")
        else:
            print("❌ Not found in database after save")
            
        return True
    else:
        print(f"❌ Save failed: {response.text}")
        return False

if __name__ == '__main__':
    test_save_learning_time()
