"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Sidebar } from "@/components/sidebar"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Menu, Sun, Moon, HelpCircle, Crown, LogOut } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { useTheme } from "@/components/theme-provider"
import { navigationEvents } from "@/lib/navigation-events"

interface LayoutWithSidebarProps {
  children: React.ReactNode
  showQuizButton?: boolean
  showUpgradeButton?: boolean
  showUserEmail?: boolean
  documentId?: number | null
}

export function LayoutWithSidebar({
  children,
  showQuizButton = false,
  showUpgradeButton = false,
  showUserEmail = false,
  documentId = null
}: LayoutWithSidebarProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const { theme, setTheme } = useTheme()
  const [isLoggedIn, setIsLoggedIn] = useState(false)
  const [username, setUsername] = useState("")
  const router = useRouter()

  useEffect(() => {
    // Check if user is logged in from localStorage or cookies
    const userLoggedIn = localStorage.getItem("isLoggedIn") === "true"
    const storedUsername = localStorage.getItem("username")

    if (userLoggedIn && storedUsername) {
      setIsLoggedIn(true)
      setUsername(storedUsername)
    }
  }, [])

  const handleLogout = () => {
    // In a real app, you would call your logout API here
    localStorage.removeItem("isLoggedIn")
    localStorage.removeItem("username")
    setIsLoggedIn(false)
    setUsername("")
    navigationEvents.triggerNavigation()
    router.push("/")
  }

  return (
    <div className="flex h-screen bg-background text-foreground overflow-hidden">
      {/* Sidebar */}
      <Sidebar
        isOpen={sidebarOpen}
        setIsOpen={setSidebarOpen}
        isLoggedIn={isLoggedIn}
        username={username}
        onLogout={handleLogout}
      />

      {/* Main Content */}
      <div className="flex-1 overflow-auto">
        <div className="p-4 flex justify-between items-center">
          <Button variant="ghost" size="icon" className="h-10 w-10 rounded-full" onClick={() => setSidebarOpen(true)}>
            <Menu className="h-5 w-5" />
          </Button>

          <div className="flex items-center gap-2">
            {/* User email (leftmost when enabled) */}
            {showUserEmail && isLoggedIn && (
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button className="bg-purple-600 hover:bg-purple-700">{username}</Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={handleLogout}>
                    <LogOut className="mr-2 h-4 w-4" />
                    <span>Log out</span>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            )}

            {/* Quiz button (for process page) */}
            {showQuizButton && documentId && (
              <Button
                onClick={() => {
                  navigationEvents.triggerNavigation()
                  router.push(`/quiz?documentId=${documentId}`)
                }}
                variant="outline"
                size="sm"
                className="gap-2"
              >
                <HelpCircle className="h-4 w-4" />
                Quiz
              </Button>
            )}

            {/* Upgrade button (middle) */}
            {showUpgradeButton && (
              <Button
                onClick={() => {
                  navigationEvents.triggerNavigation()
                  router.push("/subscription")
                }}
                className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white border-0"
                size="sm"
              >
                <Crown className="h-4 w-4 mr-2" />
                Upgrade
              </Button>
            )}

            {/* Theme button (rightmost) */}
            <Button
              variant="ghost"
              size="icon"
              className="h-10 w-10 rounded-full"
              onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
            >
              {theme === "dark" ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
            </Button>
          </div>
        </div>
        {children}
      </div>
    </div>
  )
}
