from django.contrib.auth.models import AbstractUser, Group, Permission
from django.db import models
from django.utils import timezone
from django.core.cache import cache
from datetime import timedelta
from django.db.models.signals import post_save
from django.dispatch import receiver
import pytz

class Student(AbstractUser):
    is_paid = models.BooleanField(default=False)

    email = models.EmailField(unique=True)
    is_email_verified = models.BooleanField(default=False)
    email_verification_token = models.CharField(max_length=100, null=True, blank=True)
    email_verification_sent_at = models.DateTimeField(null=True, blank=True)
    otp = models.CharField(max_length=6, null=True, blank=True)
    otp_created_at = models.DateTimeField(null=True, blank=True)

    USERNAME_FIELD = 'email'  # Login using email instead of username
    REQUIRED_FIELDS = ['username']  # username is still required in form

    groups = models.ManyToManyField(
        Group,
        related_name='student_users',
        blank=True,
        help_text='The groups this user belongs to.',
        verbose_name='groups'
    )
    user_permissions = models.ManyToManyField(
        Permission,
        related_name='student_users',
        blank=True,
        help_text='Specific permissions for this user.',
        verbose_name='user permissions'
    )

    def __str__(self):
        return self.email

    def generate_verification_token(self):
        import secrets
        self.email_verification_token = secrets.token_urlsafe(32)
        self.email_verification_sent_at = timezone.now()
        self.save()
        return self.email_verification_token

    def generate_otp(self):
        import random
        import string
        self.otp = ''.join(random.choices(string.digits, k=6))
        self.otp_created_at = timezone.now()
        self.save()
        return self.otp

    def verify_otp(self, otp):
        if not self.otp or not self.otp_created_at:
            return False

        # Check if OTP is expired (10 minutes validity)
        if timezone.now() > self.otp_created_at + timezone.timedelta(minutes=10):
            return False

        return self.otp == otp

@receiver(post_save, sender=Student)
def create_user_profile(sender, instance, created, **kwargs):
    if created:
        UserProfile.objects.create(user=instance)

@receiver(post_save, sender=Student)
def save_user_profile(sender, instance, **kwargs):
    if not hasattr(instance, 'userprofile'):
        UserProfile.objects.create(user=instance)
    instance.userprofile.save()

class UserProfile(models.Model):
    user = models.OneToOneField('Student', on_delete=models.CASCADE)
    is_paid = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.user.username} - {'Paid' if self.is_paid else 'Free'}"

class UserUsage(models.Model):
    user = models.ForeignKey('Student', on_delete=models.CASCADE)
    date = models.DateField(default=timezone.now)
    chat_count = models.IntegerField(default=0)
    file_upload_count = models.IntegerField(default=0)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('user', 'date')
        indexes = [
            models.Index(fields=['user', 'date']),
            models.Index(fields=['date']),  # For efficient cleanup
        ]

    def __str__(self):
        return f"{self.user.username} - {self.date} - Chats: {self.chat_count}, Files: {self.file_upload_count}"

    @property
    def chat_limit(self):
        return 100 if self.user.userprofile.is_paid else 5000

    @property
    def file_upload_limit(self):
        return 5 if self.user.userprofile.is_paid else 100

    def can_make_chat(self):
        return self.chat_count < self.chat_limit

    def can_upload_file(self):
        return self.file_upload_count < self.file_upload_limit

    def increment_chat_count(self):
        self.chat_count = self.chat_count + 1
        UserUsage.objects.filter(id=self.id).update(chat_count=self.chat_count)
        self._update_cache()

    def increment_file_upload_count(self):
        self.file_upload_count = self.file_upload_count + 1
        UserUsage.objects.filter(id=self.id).update(file_upload_count=self.file_upload_count)
        self._update_cache()

    def _update_cache(self):
        """Update cache with current usage data"""
        cache_key = f"user_usage_{self.user.id}_{self.date}"
        cache.set(cache_key, {
            'chat_count': self.chat_count,
            'file_upload_count': self.file_upload_count,
            'chat_limit': self.chat_limit,
            'file_upload_limit': self.file_upload_limit
        }, timeout=86400)  # Cache for 24 hours

    @classmethod
    def get_or_create_usage(cls, user, date=None):
        """Get or create usage record with caching"""
        if date is None:
            date = timezone.now().date()

        cache_key = f"user_usage_{user.id}_{date}"
        cached_data = cache.get(cache_key)

        if cached_data:
            usage, _ = cls.objects.get_or_create(
                user=user,
                date=date,
                defaults={
                    'chat_count': cached_data['chat_count'],
                    'file_upload_count': cached_data['file_upload_count']
                }
            )
            return usage

        usage, created = cls.objects.get_or_create(
            user=user,
            date=date,
            defaults={'chat_count': 0, 'file_upload_count': 0}
        )

        if created:
            usage._update_cache()

        return usage

    @classmethod
    def cleanup_old_records(cls, days_to_keep=30):
        """Efficient cleanup of old records using bulk operations"""
        cutoff_date = timezone.now().date() - timedelta(days=days_to_keep)

        # Get IDs of records to delete
        old_record_ids = list(cls.objects.filter(date__lt=cutoff_date).values_list('id', flat=True))

        if old_record_ids:
            # Delete in chunks to avoid memory issues
            chunk_size = 1000
            for i in range(0, len(old_record_ids), chunk_size):
                chunk = old_record_ids[i:i + chunk_size]
                cls.objects.filter(id__in=chunk).delete()

        return len(old_record_ids)


class StudentPerformance(models.Model):
    """
    Model to track student performance on quizzes for specific documents.
    Each entry represents a single quiz attempt with score and time taken.
    """
    student = models.ForeignKey('Student', on_delete=models.CASCADE, related_name='performances')
    document = models.ForeignKey('documents.Document', on_delete=models.CASCADE, related_name='student_performances')
    quiz_score = models.DecimalField(max_digits=5, decimal_places=2, default=0.0,
                                    help_text="Score achieved on the quiz (percentage)")
    time_taken = models.PositiveIntegerField(help_text="Time taken to complete the quiz (in seconds)")
    remarks = models.TextField(blank=True, null=True, help_text="Feedback or comments on student performance")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        indexes = [
            models.Index(fields=['student', 'document']),
            models.Index(fields=['quiz_score']),  # For performance analytics
            models.Index(fields=['created_at']),  # For chronological ordering
        ]
        ordering = ['-created_at']
        verbose_name = "Student Performance"
        verbose_name_plural = "Student Performances"

    def __str__(self):
        return f"{self.student.username} - {self.document.title} - Score: {self.quiz_score}% - {self.created_at.strftime('%Y-%m-%d %H:%M')}"


class PlatformTimeSession(models.Model):
    """
    Enhanced model to track time spent by users on specific documents.
    Each session represents a continuous period of document learning.
    Supports pause/resume functionality for quizzes and navigation.
    """
    student = models.ForeignKey('Student', on_delete=models.CASCADE, related_name='platform_sessions')
    document = models.ForeignKey('documents.Document', on_delete=models.CASCADE, related_name='time_sessions')
    session_start = models.DateTimeField(help_text="When the session started (Indian timezone)")
    session_end = models.DateTimeField(null=True, blank=True, help_text="When the session ended (Indian timezone)")
    total_time_seconds = models.PositiveIntegerField(default=0, help_text="Total time spent in seconds")
    is_active = models.BooleanField(default=True, help_text="Whether this session is currently active")
    is_paused = models.BooleanField(default=False, help_text="Whether this session is currently paused")
    pause_reason = models.CharField(max_length=50, choices=[
        ('quiz', 'Quiz Started'),
        ('navigation', 'Navigated Away'),
        ('manual', 'Manual Pause'),
    ], null=True, blank=True, help_text="Reason for pausing the session")
    last_activity = models.DateTimeField(auto_now=True, help_text="Last recorded activity")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        indexes = [
            models.Index(fields=['student', 'document']),
            models.Index(fields=['session_start']),
            models.Index(fields=['last_activity']),
        ]
        ordering = ['-session_start']
        verbose_name = "Platform Time Session"
        verbose_name_plural = "Platform Time Sessions"

    def __str__(self):
        doc_title = self.document.title if self.document else "Platform"
        return f"{self.student.username} - {doc_title} - {self.total_time_seconds}s"

    def get_indian_timezone(self):
        """Get Indian timezone object"""
        return pytz.timezone('Asia/Kolkata')

    def end_session(self):
        """End the session and calculate total time"""
        if not self.session_end:
            indian_tz = self.get_indian_timezone()
            self.session_end = timezone.now().astimezone(indian_tz)

            # Calculate total time
            if self.session_start:
                duration = self.session_end - self.session_start
                self.total_time_seconds = int(duration.total_seconds())

            self.save()

    @classmethod
    def start_session(cls, student, document=None):
        """Start a new session"""
        indian_tz = pytz.timezone('Asia/Kolkata')
        session = cls.objects.create(
            student=student,
            document=document,
            session_start=timezone.now().astimezone(indian_tz)
        )
        return session

    def pause_session(self, reason='manual'):
        """Pause the current session"""
        if self.is_active and not self.is_paused:
            self.is_paused = True
            self.pause_reason = reason
            self.save()

            # Create pause event
            PlatformTimeEvent.objects.create(
                session=self,
                event_type='pause',
                reason=reason,
                timestamp=timezone.now().astimezone(self.get_indian_timezone())
            )

    def resume_session(self):
        """Resume the paused session"""
        if self.is_active and self.is_paused:
            self.is_paused = False
            self.pause_reason = None
            self.save()

            # Create resume event
            PlatformTimeEvent.objects.create(
                session=self,
                event_type='resume',
                timestamp=timezone.now().astimezone(self.get_indian_timezone())
            )

    @classmethod
    def get_active_session(cls, student, document):
        """Get current active session for user and document"""
        return cls.objects.filter(
            student=student,
            document=document,
            is_active=True
        ).first()

    @classmethod
    def pause_all_other_sessions(cls, student, current_document):
        """Pause all active sessions except for the current document"""
        other_sessions = cls.objects.filter(
            student=student,
            is_active=True,
            is_paused=False
        ).exclude(document=current_document)

        for session in other_sessions:
            session.pause_session(reason='navigation')

class PlatformTimeEvent(models.Model):
    """
    Model to track all time tracking events (start, pause, resume, end).
    Provides detailed audit trail of user learning sessions.
    """
    session = models.ForeignKey('PlatformTimeSession', on_delete=models.CASCADE, related_name='events')
    event_type = models.CharField(max_length=20, choices=[
        ('start', 'Session Started'),
        ('pause', 'Session Paused'),
        ('resume', 'Session Resumed'),
        ('end', 'Session Ended'),
    ], help_text="Type of time tracking event")
    reason = models.CharField(max_length=50, choices=[
        ('quiz', 'Quiz Started'),
        ('navigation', 'Navigated Away'),
        ('manual', 'Manual Action'),
        ('completion', 'Session Completed'),
    ], null=True, blank=True, help_text="Reason for the event")
    timestamp = models.DateTimeField(help_text="When the event occurred (Indian timezone)")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        indexes = [
            models.Index(fields=['session', 'event_type']),
            models.Index(fields=['timestamp']),
        ]
        ordering = ['-timestamp']
        verbose_name = "Platform Time Event"
        verbose_name_plural = "Platform Time Events"

    def __str__(self):
        return f"{self.session.student.username} - {self.session.document.title} - {self.event_type} - {self.timestamp.strftime('%Y-%m-%d %H:%M')}"





class PlatformTimeStats(models.Model):
    """
    Enhanced model to store comprehensive time statistics per user per document.
    Tracks learning patterns, quiz interactions, and session details.
    """
    student = models.ForeignKey('Student', on_delete=models.CASCADE, related_name='platform_time_stats')
    document = models.ForeignKey('documents.Document', on_delete=models.CASCADE, related_name='time_stats')
    total_time_seconds = models.PositiveIntegerField(default=0, help_text="Total time spent learning (excluding quiz time)")
    total_sessions = models.PositiveIntegerField(default=0, help_text="Total number of learning sessions")
    total_pauses = models.PositiveIntegerField(default=0, help_text="Total number of times paused")
    quiz_pauses = models.PositiveIntegerField(default=0, help_text="Number of times paused for quiz")
    navigation_pauses = models.PositiveIntegerField(default=0, help_text="Number of times paused for navigation")
    view_count = models.PositiveIntegerField(default=0, help_text="Number of times document was accessed")
    first_access = models.DateTimeField(null=True, blank=True, help_text="First time accessed")
    last_access = models.DateTimeField(null=True, blank=True, help_text="Last time accessed")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('student', 'document')
        indexes = [
            models.Index(fields=['student', 'document']),
            models.Index(fields=['total_time_seconds']),
            models.Index(fields=['last_access']),
        ]
        ordering = ['-last_access']
        verbose_name = "Platform Time Statistics"
        verbose_name_plural = "Platform Time Statistics"

    def __str__(self):
        return f"{self.student.username} - {self.document.title} - {self.total_time_seconds}s"

    @property
    def average_session_time(self):
        """Calculate average session time in seconds"""
        if self.total_sessions > 0:
            return self.total_time_seconds / self.total_sessions
        return 0

    @property
    def was_reopened(self):
        """Check if document was reopened at least once"""
        return self.view_count > 1

    def get_indian_timezone(self):
        """Get Indian timezone object"""
        return pytz.timezone('Asia/Kolkata')

    def update_from_session(self, session):
        """Update stats based on a completed session"""
        self.total_time_seconds += session.total_time_seconds
        self.total_sessions += 1

        # Count pauses by type
        pause_events = session.events.filter(event_type='pause')
        self.total_pauses += pause_events.count()
        self.quiz_pauses += pause_events.filter(reason='quiz').count()
        self.navigation_pauses += pause_events.filter(reason='navigation').count()

        # Update access times
        indian_tz = self.get_indian_timezone()
        self.last_access = timezone.now().astimezone(indian_tz)

        if not self.first_access:
            self.first_access = self.last_access

        self.save()

    def increment_view_count(self):
        """Increment view count when document is accessed"""
        self.view_count += 1
        indian_tz = self.get_indian_timezone()
        self.last_access = timezone.now().astimezone(indian_tz)

        if not self.first_access:
            self.first_access = self.last_access

        self.save()

    @classmethod
    def get_or_create_stats(cls, student, document):
        """Get or create stats record for student and document"""
        indian_tz = pytz.timezone('Asia/Kolkata')
        now = timezone.now().astimezone(indian_tz)

        stats, created = cls.objects.get_or_create(
            student=student,
            document=document,
            defaults={
                'first_access': now,
                'last_access': now,
            }
        )

        if not created:
            stats.last_access = now
            stats.save(update_fields=['last_access'])

        return stats
