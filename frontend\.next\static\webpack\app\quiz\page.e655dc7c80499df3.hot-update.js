"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/quiz/page",{

/***/ "(app-pages-browser)/./hooks/use-document-time.ts":
/*!************************************!*\
  !*** ./hooks/use-document-time.ts ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useDocumentTime: () => (/* binding */ useDocumentTime)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n/* __next_internal_client_entry_do_not_use__ useDocumentTime auto */ \n\nfunction useDocumentTime() {\n    let { documentId, enabled = true, isProcessingComplete = false, onQuizStart, onQuizEnd } = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    const sessionIdRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const isActiveRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const isPausedRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    const currentDocumentRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(documentId);\n    const sessionStartTimeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_1__.useRouter)();\n    // Get the correct API base URL\n    const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000/api';\n    const getAuthHeaders = ()=>{\n        const token = localStorage.getItem('token');\n        return {\n            'Content-Type': 'application/json',\n            'Authorization': \"Token \".concat(token)\n        };\n    };\n    const startSession = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDocumentTime.useCallback[startSession]\": async ()=>{\n            if (!enabled || !documentId || !isProcessingComplete || isActiveRef.current) return;\n            try {\n                const token = localStorage.getItem('token');\n                if (!token) return;\n                const response = await fetch(\"\".concat(API_BASE_URL, \"/users/platform-time/start/\"), {\n                    method: 'POST',\n                    headers: getAuthHeaders(),\n                    body: JSON.stringify({\n                        document_id: documentId\n                    })\n                });\n                if (response.ok) {\n                    const data = await response.json();\n                    sessionIdRef.current = data.session_id;\n                    isActiveRef.current = true;\n                    isPausedRef.current = false;\n                    currentDocumentRef.current = documentId;\n                    sessionStartTimeRef.current = new Date() // Record when session started\n                    ;\n                    console.log('Document time session started:', data.session_id, data.message);\n                }\n            } catch (error) {\n                console.error('Error starting document time session:', error);\n            }\n        }\n    }[\"useDocumentTime.useCallback[startSession]\"], [\n        documentId,\n        enabled,\n        isProcessingComplete\n    ]);\n    const pauseSession = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDocumentTime.useCallback[pauseSession]\": async function() {\n            let reason = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 'manual';\n            if (!documentId || !isActiveRef.current || isPausedRef.current) return;\n            try {\n                const token = localStorage.getItem('token');\n                if (!token) return;\n                const response = await fetch(\"\".concat(API_BASE_URL, \"/users/platform-time/pause/\"), {\n                    method: 'POST',\n                    headers: getAuthHeaders(),\n                    body: JSON.stringify({\n                        document_id: documentId,\n                        reason: reason\n                    })\n                });\n                if (response.ok) {\n                    isPausedRef.current = true;\n                    console.log(\"Document time session paused: \".concat(reason));\n                }\n            } catch (error) {\n                console.error('Error pausing document time session:', error);\n            }\n        }\n    }[\"useDocumentTime.useCallback[pauseSession]\"], [\n        documentId\n    ]);\n    const resumeSession = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDocumentTime.useCallback[resumeSession]\": async ()=>{\n            if (!documentId || !isActiveRef.current || !isPausedRef.current) return;\n            try {\n                const token = localStorage.getItem('token');\n                if (!token) return;\n                const response = await fetch(\"\".concat(API_BASE_URL, \"/users/platform-time/resume/\"), {\n                    method: 'POST',\n                    headers: getAuthHeaders(),\n                    body: JSON.stringify({\n                        document_id: documentId\n                    })\n                });\n                if (response.ok) {\n                    isPausedRef.current = false;\n                    console.log('Document time session resumed');\n                }\n            } catch (error) {\n                console.error('Error resuming document time session:', error);\n            }\n        }\n    }[\"useDocumentTime.useCallback[resumeSession]\"], [\n        documentId\n    ]);\n    const endSession = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDocumentTime.useCallback[endSession]\": async ()=>{\n            if (!documentId || !isActiveRef.current) return;\n            try {\n                const token = localStorage.getItem('token');\n                if (!token) return;\n                await fetch(\"\".concat(API_BASE_URL, \"/users/platform-time/end/\"), {\n                    method: 'POST',\n                    headers: getAuthHeaders(),\n                    body: JSON.stringify({\n                        document_id: documentId\n                    })\n                });\n                sessionIdRef.current = null;\n                isActiveRef.current = false;\n                isPausedRef.current = false;\n                sessionStartTimeRef.current = null;\n                console.log('Document time session ended');\n            } catch (error) {\n                console.error('Error ending document time session:', error);\n            }\n        }\n    }[\"useDocumentTime.useCallback[endSession]\"], [\n        documentId\n    ]);\n    // Quiz control functions\n    const startQuiz = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDocumentTime.useCallback[startQuiz]\": ()=>{\n            pauseSession('quiz');\n            onQuizStart === null || onQuizStart === void 0 ? void 0 : onQuizStart();\n        }\n    }[\"useDocumentTime.useCallback[startQuiz]\"], [\n        pauseSession,\n        onQuizStart\n    ]);\n    const endQuiz = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useDocumentTime.useCallback[endQuiz]\": ()=>{\n            resumeSession();\n            onQuizEnd === null || onQuizEnd === void 0 ? void 0 : onQuizEnd();\n        }\n    }[\"useDocumentTime.useCallback[endQuiz]\"], [\n        resumeSession,\n        onQuizEnd\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useDocumentTime.useEffect\": ()=>{\n            if (!enabled || !documentId || !isProcessingComplete) return;\n            // If document changed, end previous session and start new one\n            if (currentDocumentRef.current !== documentId) {\n                if (isActiveRef.current) {\n                    endSession();\n                }\n                currentDocumentRef.current = documentId;\n            }\n            // Start session only when processing is complete (learning phase begins)\n            startSession();\n            // Handle page unload - end session when user leaves\n            const handleBeforeUnload = {\n                \"useDocumentTime.useEffect.handleBeforeUnload\": ()=>{\n                    endSession();\n                }\n            }[\"useDocumentTime.useEffect.handleBeforeUnload\"];\n            window.addEventListener('beforeunload', handleBeforeUnload);\n            return ({\n                \"useDocumentTime.useEffect\": ()=>{\n                    window.removeEventListener('beforeunload', handleBeforeUnload);\n                }\n            })[\"useDocumentTime.useEffect\"];\n        }\n    }[\"useDocumentTime.useEffect\"], [\n        documentId,\n        enabled,\n        isProcessingComplete,\n        startSession,\n        endSession\n    ]);\n    // Add cleanup effect for component unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useDocumentTime.useEffect\": ()=>{\n            return ({\n                \"useDocumentTime.useEffect\": ()=>{\n                    // End session when component unmounts (user navigates away)\n                    if (isActiveRef.current) {\n                        endSession();\n                    }\n                }\n            })[\"useDocumentTime.useEffect\"];\n        }\n    }[\"useDocumentTime.useEffect\"], [\n        endSession\n    ]);\n    return {\n        sessionId: sessionIdRef.current,\n        isActive: isActiveRef.current,\n        isPaused: isPausedRef.current,\n        sessionStartTime: sessionStartTimeRef.current,\n        startQuiz,\n        endQuiz,\n        pauseSession,\n        resumeSession,\n        endSession\n    };\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/use-document-time.ts\n"));

/***/ })

});