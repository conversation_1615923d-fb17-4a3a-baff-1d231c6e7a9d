# Generated by Django 4.2.21 on 2025-06-28 18:12

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('documents', '0010_rename_questionanswer_quiz'),
        ('users', '0010_documenttimesession_documenttimestats_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='PlatformTimeEvent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_type', models.CharField(choices=[('start', 'Session Started'), ('pause', 'Session Paused'), ('resume', 'Session Resumed'), ('end', 'Session Ended')], help_text='Type of time tracking event', max_length=20)),
                ('reason', models.CharField(blank=True, choices=[('quiz', 'Quiz Started'), ('navigation', 'Navigated Away'), ('manual', 'Manual Action'), ('completion', 'Session Completed')], help_text='Reason for the event', max_length=50, null=True)),
                ('timestamp', models.DateTimeField(help_text='When the event occurred (Indian timezone)')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Platform Time Event',
                'verbose_name_plural': 'Platform Time Events',
                'ordering': ['-timestamp'],
            },
        ),
        migrations.CreateModel(
            name='PlatformTimeSession',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('session_start', models.DateTimeField(help_text='When the session started (Indian timezone)')),
                ('session_end', models.DateTimeField(blank=True, help_text='When the session ended (Indian timezone)', null=True)),
                ('total_time_seconds', models.PositiveIntegerField(default=0, help_text='Total time spent in seconds')),
                ('is_active', models.BooleanField(default=True, help_text='Whether this session is currently active')),
                ('is_paused', models.BooleanField(default=False, help_text='Whether this session is currently paused')),
                ('pause_reason', models.CharField(blank=True, choices=[('quiz', 'Quiz Started'), ('navigation', 'Navigated Away'), ('manual', 'Manual Pause')], help_text='Reason for pausing the session', max_length=50, null=True)),
                ('last_activity', models.DateTimeField(auto_now=True, help_text='Last recorded activity')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('document', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='time_sessions', to='documents.document')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='platform_sessions', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Platform Time Session',
                'verbose_name_plural': 'Platform Time Sessions',
                'ordering': ['-session_start'],
            },
        ),
        migrations.CreateModel(
            name='PlatformTimeStats',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_time_seconds', models.PositiveIntegerField(default=0, help_text='Total time spent learning (excluding quiz time)')),
                ('total_sessions', models.PositiveIntegerField(default=0, help_text='Total number of learning sessions')),
                ('total_pauses', models.PositiveIntegerField(default=0, help_text='Total number of times paused')),
                ('quiz_pauses', models.PositiveIntegerField(default=0, help_text='Number of times paused for quiz')),
                ('navigation_pauses', models.PositiveIntegerField(default=0, help_text='Number of times paused for navigation')),
                ('view_count', models.PositiveIntegerField(default=0, help_text='Number of times document was accessed')),
                ('first_access', models.DateTimeField(blank=True, help_text='First time accessed', null=True)),
                ('last_access', models.DateTimeField(blank=True, help_text='Last time accessed', null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('document', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='time_stats', to='documents.document')),
                ('student', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='platform_time_stats', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Platform Time Statistics',
                'verbose_name_plural': 'Platform Time Statistics',
                'ordering': ['-last_access'],
            },
        ),
        migrations.AlterUniqueTogether(
            name='documenttimestats',
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name='documenttimestats',
            name='document',
        ),
        migrations.RemoveField(
            model_name='documenttimestats',
            name='student',
        ),
        migrations.DeleteModel(
            name='DocumentTimeSession',
        ),
        migrations.DeleteModel(
            name='DocumentTimeStats',
        ),
        migrations.AddField(
            model_name='platformtimeevent',
            name='session',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='events', to='users.platformtimesession'),
        ),
        migrations.AddIndex(
            model_name='platformtimestats',
            index=models.Index(fields=['student', 'document'], name='users_platf_student_a71a97_idx'),
        ),
        migrations.AddIndex(
            model_name='platformtimestats',
            index=models.Index(fields=['total_time_seconds'], name='users_platf_total_t_1d632c_idx'),
        ),
        migrations.AddIndex(
            model_name='platformtimestats',
            index=models.Index(fields=['last_access'], name='users_platf_last_ac_b3d5a8_idx'),
        ),
        migrations.AlterUniqueTogether(
            name='platformtimestats',
            unique_together={('student', 'document')},
        ),
        migrations.AddIndex(
            model_name='platformtimesession',
            index=models.Index(fields=['student', 'document'], name='users_platf_student_7e9574_idx'),
        ),
        migrations.AddIndex(
            model_name='platformtimesession',
            index=models.Index(fields=['session_start'], name='users_platf_session_2f6e60_idx'),
        ),
        migrations.AddIndex(
            model_name='platformtimesession',
            index=models.Index(fields=['last_activity'], name='users_platf_last_ac_d6da23_idx'),
        ),
        migrations.AddIndex(
            model_name='platformtimeevent',
            index=models.Index(fields=['session', 'event_type'], name='users_platf_session_5d4389_idx'),
        ),
        migrations.AddIndex(
            model_name='platformtimeevent',
            index=models.Index(fields=['timestamp'], name='users_platf_timesta_dd8565_idx'),
        ),
    ]
