#!/usr/bin/env python3
"""
Debug script to test the learning time prediction step by step.
"""

import requests
import json

def test_fastapi_django_connection():
    """Test if FastAPI can connect to Django"""
    try:
        # Test the Django connection endpoint
        response = requests.get("http://localhost:8001/test-django-connection")
        print(f"Django connection test: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"Connection result: {result}")
            return True
        else:
            print(f"Connection failed: {response.text}")
            return False
    except Exception as e:
        print(f"Error testing Django connection: {str(e)}")
        return False

def test_document_embeddings(document_id, auth_token):
    """Test if we can retrieve document embeddings"""
    try:
        # Test retrieving embeddings directly from Django
        django_url = f"http://localhost:8000/api/documents/{document_id}/embeddings/"
        headers = {"Authorization": f"Token {auth_token}"}
        
        response = requests.get(django_url, headers=headers)
        print(f"Django embeddings retrieval: {response.status_code}")
        
        if response.status_code == 200:
            embeddings = response.json()
            print(f"Found {len(embeddings)} embeddings")
            if embeddings:
                print(f"First embedding preview: {embeddings[0]['text'][:100]}...")
            return embeddings
        else:
            print(f"Failed to retrieve embeddings: {response.text}")
            return None
            
    except Exception as e:
        print(f"Error retrieving embeddings: {str(e)}")
        return None

def test_fastapi_embeddings_retrieval(document_id, auth_token):
    """Test if FastAPI can retrieve embeddings from Django"""
    try:
        # Test the FastAPI helper function
        url = f"http://localhost:8001/retrieve-context-for-document/{document_id}"
        headers = {"Authorization": f"Bearer {auth_token}"}
        
        response = requests.get(url, headers=headers)
        print(f"FastAPI embeddings retrieval: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"FastAPI retrieved context: {len(result.get('context', []))} items")
            return result
        else:
            print(f"FastAPI embeddings retrieval failed: {response.text}")
            return None
            
    except Exception as e:
        print(f"Error testing FastAPI embeddings retrieval: {str(e)}")
        return None

def test_learning_time_prediction_step_by_step(document_id, auth_token):
    """Test learning time prediction step by step"""
    try:
        print(f"\n=== Testing Learning Time Prediction for Document {document_id} ===")
        
        # Step 1: Test Django connection
        print("\nStep 1: Testing FastAPI-Django connection...")
        django_ok = test_fastapi_django_connection()
        
        # Step 2: Test direct Django embeddings retrieval
        print("\nStep 2: Testing direct Django embeddings retrieval...")
        embeddings = test_document_embeddings(document_id, auth_token)
        
        # Step 3: Test FastAPI embeddings retrieval
        print("\nStep 3: Testing FastAPI embeddings retrieval...")
        fastapi_context = test_fastapi_embeddings_retrieval(document_id, auth_token)
        
        # Step 4: Test the actual learning time prediction
        print("\nStep 4: Testing learning time prediction...")
        url = f"http://localhost:8001/predict-learning-time/{document_id}"
        headers = {"Authorization": f"Bearer {auth_token}"}
        payload = {
            "document_id": document_id,
            "llm_model": "gemini",
            "force_regenerate": True
        }
        
        print(f"Making request to: {url}")
        print(f"Payload: {payload}")
        
        response = requests.post(url, json=payload, headers=headers, timeout=60)
        print(f"Learning time prediction: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✓ Success! Predicted time: {result.get('time_range_display', 'N/A')}")
            return True
        else:
            print(f"❌ Failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"Error in step-by-step test: {str(e)}")
        return False

def main():
    print("=" * 60)
    print("LEARNING TIME PREDICTION DEBUG")
    print("=" * 60)
    
    # Use a known document and auth token
    document_id = 81  # RL_Unit 4.pdf from our earlier test
    auth_token = "458168605e4bfaff26ac7bce4238ba4f813ae021"  # Token for user who owns document 81
    
    success = test_learning_time_prediction_step_by_step(document_id, auth_token)
    
    print("\n" + "=" * 60)
    print("DEBUG SUMMARY")
    print("=" * 60)
    print(f"Overall result: {'✓ SUCCESS' if success else '❌ FAILED'}")

if __name__ == '__main__':
    main()
