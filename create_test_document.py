#!/usr/bin/env python3
"""
Create a test document with embeddings for the current user.
"""

import os
import sys
import django
from pathlib import Path

# Add the Django project to the path
core_path = str(Path(__file__).parent / 'core')
sys.path.insert(0, core_path)

# Change to core directory for Django setup
original_cwd = os.getcwd()
os.chdir(core_path)

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

# Change back to original directory
os.chdir(original_cwd)

from django.contrib.auth import get_user_model
from documents.models import Document, DocumentEmbedding
from rest_framework.authtoken.models import Token

User = get_user_model()

def create_test_document():
    """Create a test document with embeddings for testing"""
    try:
        # Get the user (jovan)
        user = User.objects.get(username='jovan')
        print(f"✓ Found user: {user.username}")
        
        # Create a test document
        document = Document.objects.create(
            user=user,
            title="Test Document for Learning Time Prediction",
            file_type="txt",
            processing_status="completed"
        )
        print(f"✓ Created document: {document.title} (ID: {document.id})")
        
        # Create some test embeddings
        test_content = [
            "Artificial Intelligence (AI) is a branch of computer science that aims to create intelligent machines.",
            "Machine learning is a subset of AI that enables computers to learn and improve from experience.",
            "Deep learning uses neural networks with multiple layers to process and analyze complex data.",
            "Natural language processing (NLP) helps computers understand and interpret human language.",
            "Computer vision allows machines to interpret and understand visual information from the world."
        ]
        
        for i, text in enumerate(test_content):
            embedding = DocumentEmbedding.objects.create(
                document=document,
                text_chunk=text,
                chunk_number=i + 1,
                embedding_vector=f"[0.1, 0.2, 0.3]"  # Dummy embedding vector
            )
            print(f"✓ Created embedding {i + 1}: {text[:50]}...")
        
        print(f"✓ Document {document.id} created with {len(test_content)} embeddings")
        return document.id
        
    except Exception as e:
        print(f"❌ Error creating test document: {str(e)}")
        return None

def main():
    print("=" * 60)
    print("CREATING TEST DOCUMENT")
    print("=" * 60)
    
    document_id = create_test_document()
    
    if document_id:
        print(f"\n🎉 Test document created successfully!")
        print(f"Document ID: {document_id}")
        print(f"You can now test learning time prediction with this document.")
    else:
        print(f"\n❌ Failed to create test document.")

if __name__ == '__main__':
    main()
