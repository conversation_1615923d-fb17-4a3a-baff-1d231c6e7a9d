"use client"

import { useState, useEffect, useRef } from "react"
import { use<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, use<PERSON><PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from "@/components/ui/resizable"
import { Button } from "@/components/ui/button"
import {
  MessageSquare,
  FileText,
  FileLineChartIcon as Flow<PERSON><PERSON>,
  BookOpen,
  BarChart4,
  FlashlightIcon as Flashcard,
} from "lucide-react"
import { UploadContent } from "@/components/upload-content"
import { PasteContent } from "@/components/paste-content"
import { RecordContent } from "@/components/record-content"
import { ChatInterface } from "@/components/chat-interface"
import { PerformanceDashboard } from "@/components/performance-dashboard"
import { motion } from "framer-motion"
import { LayoutWithSidebar } from "@/components/layout-with-sidebar"
import { useTheme } from "@/components/theme-provider"
import { BlueprintInterface } from "@/components/blueprint-interface"

import { FlowchartInterface } from "@/components/flowchart-interface"
import { FlashcardsInterface } from "@/components/flashcards-interface"
import { SummaryInterface } from "@/components/summary-interface"
import { ChaptersInterface } from "@/components/chapters-interface"
import { LoadingOverlay } from "@/components/loading-overlay"
import { FilePreview } from "@/components/file-preview"
import { useSimpleTimer } from "@/hooks/use-simple-timer"
import { documentApi } from "@/lib/api"

type Message = {
  id: string
  content: string
  role: "user" | "assistant"
  timestamp: Date
}

export default function ProcessPage() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const type = searchParams.get("type") || "upload"
  const [activeTab, setActiveTab] = useState("chat")
  const [showFullScreen, setShowFullScreen] = useState(true)
  const [uploadedData, setUploadedData] = useState<any>(null)
  const { theme } = useTheme()
  const tabsListRef = useRef<HTMLDivElement>(null)

  // State preservation
  const [chatState, setChatState] = useState<Message[]>([])
  const [summaryState] = useState("")
  const [chapterState] = useState("")
  const [isProcessing, setIsProcessing] = useState(false)
  const [documentDetails, setDocumentDetails] = useState<any>(null)

  useEffect(() => {
    document.body.classList.add("page-transition")

    // Check if we have a document ID (either from upload or clicking existing document)
    const documentId = searchParams.get("documentId")
    if (documentId) {
      const docId = parseInt(documentId)

      // If it's a new upload, show processing
      if (type === "upload") {
        setIsProcessing(true)
        // Check document processing status
        checkDocumentStatus(docId)
      }

      // Always load document details for preview
      loadDocumentDetails(docId)
    }

    const uploadedFilesStr = localStorage.getItem("uploadedFiles")

    if (uploadedFilesStr && type === "upload") {
      try {
        const uploadedFiles = JSON.parse(uploadedFilesStr)
        if (uploadedFiles.length > 0) {
          const file = uploadedFiles[0]
          const storedDocumentId = localStorage.getItem("currentDocumentId")

          setUploadedData({
            type: "file",
            name: file.name,
            size: file.size,
            fileType: file.type || file.name.split('.').pop()?.toLowerCase(),
            documentId: file.documentId || (storedDocumentId ? parseInt(storedDocumentId) : undefined)
          })
          setShowFullScreen(false)
        }
      } catch (error) {
        console.error("Error parsing uploaded files:", error)
      }
    } else if (type === "paste") {
      const pastedContentStr = localStorage.getItem("pastedContent")
      if (pastedContentStr) {
        try {
          const pastedContent = JSON.parse(pastedContentStr)
          setUploadedData({
            type: "text",
            content: pastedContent.content || pastedContent.url || "Pasted content",
          })
          setShowFullScreen(false)
        } catch (error) {
          console.error("Error parsing pasted content:", error)
        }
      }
    } else if (type === "record") {
      const recordedAudioStr = localStorage.getItem("recordedAudio")
      if (recordedAudioStr) {
        try {
          const recordedAudio = JSON.parse(recordedAudioStr)
          setUploadedData({
            type: "audio",
            name: "Recording-" + new Date().toISOString().split("T")[0] + ".wav",
            duration: recordedAudio.duration || "00:00",
          })
          setShowFullScreen(false)
        } catch (error) {
          console.error("Error parsing recorded audio:", error)
        }
      }
    }

    return () => {
      document.body.classList.remove("page-transition")
    }
  }, [type, searchParams])

  const loadDocumentDetails = async (documentId: number) => {
    try {
      const response = await documentApi.getDocumentDetails(documentId)
      setDocumentDetails(response)

      // Create file preview data from document details
      if (response && response.file) {
        const fileExtension = response.title.split('.').pop()?.toLowerCase()

        const fileData = {
          type: "file",
          name: response.title,
          size: "Unknown", // Backend doesn't provide file size in current response
          fileType: fileExtension || 'unknown',
          documentId: documentId // Store document ID for FilePreview component
        }

        setUploadedData(fileData)
        setShowFullScreen(false)
      }
    } catch (error) {
      console.error('Error loading document details:', error)
    }
  }

  const checkDocumentStatus = async (documentId: number) => {
    try {
      const response = await documentApi.getDocumentStatus(documentId)

      if (response.processing_status === 'completed') {
        setIsProcessing(false)
      } else if (response.processing_status === 'failed') {
        setIsProcessing(false)
        // Could show an error message here
      } else {
        // Still processing, check again in 2 seconds
        setTimeout(() => checkDocumentStatus(documentId), 2000)
      }
    } catch (error) {
      console.error('Error checking document status:', error)
      setIsProcessing(false)
    }
  }

  const renderInputComponent = () => {
    // Check if we have a document ID but no uploaded data yet
    const documentId = searchParams.get("documentId")
    if (documentId && !uploadedData && !isProcessing) {
      return (
        <div className="p-4 h-full flex flex-col items-center justify-center">
          <div className={`rounded-lg p-8 text-center ${theme === "light" ? "bg-white border border-black" : "bg-neutral-800"}`}>
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-500 mx-auto mb-4"></div>
            <p className="text-lg mb-2">Loading document...</p>
            <p className={`text-sm ${theme === "light" ? "text-neutral-600" : "text-neutral-500"}`}>
              Please wait while we load your document for preview.
            </p>
          </div>
        </div>
      )
    }

    if (uploadedData) {
      if (uploadedData.type === "file") {
        return (
          <div className="p-4 h-full flex flex-col">
            <h2 className="text-xl font-semibold mb-4">{uploadedData.name}</h2>
            <div className="flex-1 overflow-hidden">
              <FilePreview
                documentId={uploadedData.documentId}
                fileName={uploadedData.name}
                fileType={uploadedData.fileType}
                className="h-full"
              />
            </div>
          </div>
        )
      } else if (uploadedData.type === "text") {
        return (
          <div className="p-4 h-full flex flex-col">
            <h2 className="text-xl font-semibold mb-4">Pasted Content</h2>
            <div className="flex-1 overflow-auto">
              <div className={`rounded-lg p-4 ${theme === "light" ? "bg-white border border-black" : "bg-neutral-800"}`}>
                <p>{uploadedData.content}</p>
              </div>
            </div>
          </div>
        )
      } else if (uploadedData.type === "audio") {
        return (
          <div className="p-4">
            <h2 className="text-xl font-semibold mb-4">{uploadedData.name}</h2>
            <div className={`rounded-lg p-4 flex flex-col items-center ${
              theme === "light" ? "bg-white border border-black" : "bg-neutral-800"
            }`}>
              <div className={`w-full h-24 rounded-md mb-4 ${theme === "light" ? "bg-gray-200" : "bg-neutral-700"}`} />
              <audio controls className="w-full">
                <source src="#" type="audio/wav" />
              </audio>
              <p className={`text-sm mt-2 ${theme === "light" ? "text-neutral-600" : "text-neutral-500"}`}>
                Duration: {uploadedData.duration}
              </p>
            </div>
          </div>
        )
      }
    }

    switch (type) {
      case "upload": return <UploadContent />
      case "paste": return <PasteContent />
      case "record": return <RecordContent />
      default: return <UploadContent />
    }
  }

  const renderOutputComponent = () => {
    // Try to get document ID from URL params first, then from localStorage
    let documentId = searchParams.get("documentId") ? parseInt(searchParams.get("documentId")!) : undefined

    if (!documentId) {
      const storedDocumentId = localStorage.getItem("currentDocumentId")
      if (storedDocumentId) {
        documentId = parseInt(storedDocumentId)
      }
    }

    switch (activeTab) {
      case "chat":
        return <ChatInterface state={chatState} setState={setChatState} documentId={documentId} />;
      case "summary":
        return <SummaryInterface documentId={documentId} />;
      case "flowchart":
        return <FlowchartInterface documentId={documentId} />;
      case "flashcards":
        return <FlashcardsInterface documentId={documentId} />;
      case "chapters":
        return <ChaptersInterface documentId={documentId} />;
      case "transcript":
        return <PerformanceDashboard documentId={documentId} />;
      case "blueprint":
        return <BlueprintInterface documentId={documentId} />;
      default:
        return <ChatInterface state={chatState} setState={setChatState} documentId={documentId} />;

    }
  }

  // Simple document time tracking
  const documentId = searchParams.get("documentId") ? parseInt(searchParams.get("documentId")!) : undefined;
  const fileName = documentDetails?.title;
  useSimpleTimer({
    fileName: fileName,
    enabled: !!documentId && !!fileName && fileName.trim() !== '',
    isProcessingComplete: !isProcessing, // Only start tracking after processing is complete
  });



  return (
    <LayoutWithSidebar
      showQuizButton={true}
      showUpgradeButton={true}
      documentId={documentId}
    >
        <LoadingOverlay
          isVisible={isProcessing}
          message="Processing your document. This may take a few moments..."
        />
        <motion.div
          className="h-[calc(100vh-65px)] overflow-hidden bg-background text-foreground flex flex-col"
          initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.3 }}
        >
        {showFullScreen ? (
          <motion.div className="flex-1 p-4">{renderInputComponent()}</motion.div>
        ) : (
          <ResizablePanelGroup direction="horizontal" className="flex-1 overflow-hidden">
            <ResizablePanel defaultSize={40} minSize={30}>
              <div className="h-full overflow-auto">{renderInputComponent()}</div>
            </ResizablePanel>
            <ResizableHandle withHandle />
            <ResizablePanel defaultSize={60} minSize={30}>
              <div className="h-full flex flex-col">
                <Tabs defaultValue="chat" onValueChange={setActiveTab} value={activeTab} className="w-full h-full">
                  <div className="border-b border-border sticky top-0 bg-background z-10">
                    <div className="px-4 overflow-x-auto scrollbar-hide flex items-center justify-between" ref={tabsListRef}>
                      <TabsList className="h-12 w-max">
                        <TabsTrigger value="chat" className="gap-2">
                          <MessageSquare className="h-4 w-4" />Chat
                        </TabsTrigger>
                        <TabsTrigger value="blueprint" className="gap-2">
                          <FlowChart className="h-4 w-4" />Blueprint
                        </TabsTrigger>
                        <TabsTrigger value="summary" className="gap-2">
                          <FileText className="h-4 w-4" />Summary
                        </TabsTrigger>
                        <TabsTrigger value="flowchart" className="gap-2">
                          <FlowChart className="h-4 w-4" />Flowchart
                        </TabsTrigger>
                        <TabsTrigger value="flashcards" className="gap-2">
                          <Flashcard className="h-4 w-4" />Flashcards
                        </TabsTrigger>
                        <TabsTrigger value="chapters" className="gap-2">
                          <BookOpen className="h-4 w-4" />Chapters
                        </TabsTrigger>
                        <TabsTrigger value="transcript" className="gap-2">
                          <BarChart4 className="h-4 w-4" />Performance
                        </TabsTrigger>
                      </TabsList>
                      {/* Timer is now running in background - no visible display needed */}
                    </div>
                  </div>

                  <TabsContent value={activeTab} className="flex-1 p-0 m-0 overflow-hidden h-full">
                    <div className="h-full overflow-y-auto pb-8 scrollbar-thin scrollbar-thumb-neutral-500 scrollbar-track-transparent hover:scrollbar-thumb-neutral-400">{renderOutputComponent()}</div>
                  </TabsContent>
                </Tabs>
              </div>
            </ResizablePanel>
          </ResizablePanelGroup>
        )}
      </motion.div>
    </LayoutWithSidebar>
  )
}
