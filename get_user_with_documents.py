#!/usr/bin/env python3
"""
Find a user who has documents and get their auth token.
"""

import os
import sys
import django
from pathlib import Path

# Add the Django project to the path
core_path = str(Path(__file__).parent / 'core')
sys.path.insert(0, core_path)

# Change to core directory for Django setup
original_cwd = os.getcwd()
os.chdir(core_path)

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'core.settings')
django.setup()

# Change back to original directory
os.chdir(original_cwd)

from django.contrib.auth import get_user_model
from documents.models import Document
from rest_framework.authtoken.models import Token

User = get_user_model()

def main():
    print("=" * 60)
    print("FINDING USER WITH DOCUMENTS")
    print("=" * 60)
    
    try:
        # Find documents with embeddings
        documents_with_embeddings = Document.objects.filter(
            processing_status='completed',
            embeddings__isnull=False
        ).distinct()
        
        print(f"Found {documents_with_embeddings.count()} documents with embeddings")
        
        for doc in documents_with_embeddings[:3]:
            print(f"\nDocument ID: {doc.id}")
            print(f"Title: {doc.title}")
            print(f"User: {doc.user.username}")
            print(f"Embeddings count: {doc.embeddings.count()}")
            
            # Get or create token for this user
            token, created = Token.objects.get_or_create(user=doc.user)
            print(f"Auth token: {token.key}")
            
            # Test if this token can access the document
            import requests
            response = requests.get(
                f'http://localhost:8000/api/documents/{doc.id}/embeddings/',
                headers={'Authorization': f'Token {token.key}'}
            )
            print(f"API access test: {response.status_code}")
            
            if response.status_code == 200:
                embeddings = response.json()
                print(f"✓ Successfully retrieved {len(embeddings)} embeddings")
                print(f"Use this for testing:")
                print(f"  Document ID: {doc.id}")
                print(f"  Auth Token: {token.key}")
                break
            else:
                print(f"❌ Failed to access: {response.text[:100]}")
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")

if __name__ == '__main__':
    main()
